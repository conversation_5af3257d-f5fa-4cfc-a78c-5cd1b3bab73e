   <div class="inner-hero" style="background-image: url({{ Theme::asset()->url('img/bg/inner-hero-bg.jpg') }});">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 m-auto text-center">
                <div class="inner-main-heading">
                    <h1 class="text-white">{{ $portfolio->name }}</h1>
                    <div class="breadcrumbs-pages">
                        <ul>
                            <li><a href="{{url('')}}" class="text-white">Home</a></li>
                            <li class="angle"><i class="fa-solid fa-angle-right text-white"></i></li>
                            <li class="text-white">Portfolio</li>
                            <li class="angle"><i class="fa-solid fa-angle-right text-white"></i></li>
                            <li class="text-white">{{ $portfolio->name }} </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
   </div>
   <style>
       .features-section {
            background: #f4f4f4;
            padding: 3rem;
            border-radius: 15px;
            margin-bottom: 3rem;
        }

        .features-title {
            text-align: center;
            font-size: 2rem;
            color: #333;
            margin-bottom: 2rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .feature-card {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #505050;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 80px;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .feature-card h4 {
            color: #333;
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .feature-card p {
            color: #666;
            font-size: 0.9rem;
            margin: 0;
            line-height: 1.5;
        }
        .portiofolio-image-item {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
._sidebar-widget ._contact-form button.theme-btn1 {
    margin-top: 20px;
}
   </style>

   <div class="portfolio-details-area sp">
    <div class="auto-container">
        <div class="row">
            <div class="col-lg-8">
                <div class="blog-details-content ml-30 md:ml-0 sm:ml-0">

                  <article>
                     <div class="details-content">
                         <div class="about8-image _relative overflow-hidden reveal sec-bg1 border-radius mb-3" style="border-bottom: 3px solid #f4f4f4;">
                             <img class="w-full" src="{{ RvMedia::getImageUrl($portfolio->image, 'full', false, RvMedia::getDefaultImage()) }}" alt="">
                         </div>
                         @php
                            $featuresRaw = $portfolio->features;

                            if (is_string($featuresRaw)) {
                                $features = json_decode($featuresRaw, true);
                            } elseif (is_array($featuresRaw)) {
                                $features = $featuresRaw;
                            } else {
                                $features = [];
                            }
                        @endphp
                       @if (!empty($features))
                         <section class="features-section">
                            <div class="features-grid">
                                @foreach ($portfolio->features as $feature)
                                <div class="feature-card">
                                    <h4>{{ $feature[0]['value'] }}</h4>
                                </div>
                                @endforeach
                            </div>
                        </section>
                        @endif
                         <div class="heading2 mt-24">
                             <h3>Project Overview</h3>
                             <div class="ck-content mt-16">

                                {!! BaseHelper::clean($portfolio->content) !!}

                            </div>
                         </div>


                     </div>
                  </article>

@php
    $gallery = gallery_meta_data($portfolio);
@endphp
                  @if ($gallery)
                  <article>
                     <div class="details-content mt-50">
                         <div class="row">
                             @foreach ($gallery as $image)
                                @if($image)
                                <div class="col-md-12">
                                    <div class="about8-image _relative overflow-hidden reveal sec-bg1 border-radius mb-3 portiofolio-image-item">
                                        <img src="{{ RvMedia::getImageUrl(Arr::get($image, 'img')) }}" alt="{{ $portfolio->name }}" class="img-fluid">
                                    </div>
                                </div>
                                @endif
                            @endforeach
                         </div>

                     </div>
                  </article>
                  @endif


                </div>
            </div>
            <div class="col-lg-4">
                <div class="sidebar-area">
                    @if ($portfolio->logo)
                        <div class="_sidebar-widget">
                         <div class="about8-image _relative overflow-hidden reveal sec-bg1 border-radius mb-3 portiofolio-image-item">
                            <img src="{{ RvMedia::getImageUrl($portfolio->logo) }}" alt="{{ $portfolio->name }}" class="img-fluid">
                        </div>
                        <a href="{{ $portfolio->website_link }}" target="_blank" class="theme-btn1 w-full text-center"> Visit Website </a>
                    </div>
                    @endif


                    <div class="_sidebar-widget _portfolio mt-40">
                        <h3>Portfolio Details</h3>
                        <div class="portfolio-list">
                            <ul>
                                <li>Project: <span>{{ $portfolio->name }}</span></li>
                                @if ($portfolio->categories->isNotEmpty() && $portfolio->categories->first())
                                    <li> Category:
                                        <span>
                                            @foreach ($portfolio->categories as $category)
                                                <a href="{{ $category->url }}">{{ $category->name }}</a>
                                                @if (!$loop->last), @endif
                                            @endforeach
                                        </span>
                                    </li>
                                @endif
                                <li>Timeframe: <span> 4 Weeks</span></li>
                                @if ($portfolio->tags->isNotEmpty() && $portfolio->tags->first())
                                    <li>Location:
                                            <span>
                                                @foreach ($portfolio->tags as $tag)
                                                    <a href="{{ $tag->url }}">{{ $tag->name }}</a>
                                                    @if (!$loop->last), @endif
                                                @endforeach
                                            </span>
                                    </li>
                                @endif
                                <li>Status: <span>Completed</span></li>

                            </ul>
                        </div>
                        <div class="protfolio-icon-list">
                            <a href="#"><i class="fa-brands fa-facebook-f"></i></a>
                            <a href="#"><i class="fa-brands fa-instagram"></i></a>
                            <a href="#"><i class="fa-brands fa-linkedin-in"></i></a>
                            <a href="#"><i class="fa-brands fa-x-twitter"></i></a>
                        </div>
                    </div>

                    <div class="_sidebar-widget _contact mt-40">
                        <h3>Get A Free Quote</h3>
                        <div class="_contact-form mt-10">
                             {!! do_shortcode('[contact-form view="theme.goalconversion::partials.contact-form"][/contact-form]') !!}
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
   </div>
