@import url(https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap);
/*
::::::::::::::::::::::::::
 TYPOGRAPHY AREA CSS
::::::::::::::::::::::::::
*/
body {
  font-size: var(--f-fs-font-16);
  font-family: var(--f-ff-font-1);
  font-weight: var(--f-fw-normal);
  background-position: center top;
  background-repeat: no-repeat;
  background-size: cover;
}

.body1 {
  background-color: var(--gc-bg-white);
  overflow-x: hidden;
}

@media screen and (max-width: 769px) {
  body.body, html {
    overflow-x: hidden !important;
  }
}
a {
  text-decoration: none;
  color: var(--gc-text-main);
}

a:hover {
  color: var(--gc-text-main);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

input,
textarea,
select,
option {
  max-width: 100%;
}

h1, h2, h3, h4, h5, h6 {
  padding: 0;
  margin: 0;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

p {
  padding: 0;
  margin: 0;
}

img {
  max-width: 100%;
  max-height: 100%;
}

a,
a:hover,
a:focus {
  outline: none;
  text-decoration: none;
}

.sp {
  padding: 100px 0px;
}

@media (max-width: 768px) {
  .sp {
    padding: 50px 0px;
  }
}
@media (max-width: 768px) {
  .sp {
    padding: 50px 0px;
  }
}

.space28 {
  height: 28px;
}
.space20 {
  height: 20px;
}

@media (max-width: 767px) {
  .space20 {
    height: 10px;
  }
}

.space80 {
  height: 80px;
}

.text-right {
  text-align: right;
}
.padding-top {
  padding-top: 120px;
}

@media (max-width: 991px) {
  .padding-top {
    padding-top: 60px;
  }
}
.w-full {
  width: 100%;
}
.text-center {
  text-align: center;
}

.font-12 {
  font-size: 12px;
}
._relative {
  position: relative;
}

.bg-cover {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

/*
 ::::::::::::::::::::::::::
  fonts area css
 ::::::::::::::::::::::::::
 */
:root {
  --gc-text-title1: #090B0E;
  --gc-text-title2: #0B0314;
  --gc-text-pera1: #3D4C5E;
  --gc-text-main: #EC2024;
  --gc-text-white: #fff;
  --gc-text-white80per: rgba(255, 255, 255, 0.8274509804);
  --gc-text-white70per: rgba(255, 255, 255, 0.6901960784);
  --gc-bg-main: #EC2024;
  --gc-bg-main1: #000000;
  --gc-bg-main2: #232323;
  --gc-bg-main3: #BD3F3D;
  --gc-bg-main4: #155FFF;
  --gc-bg-main5: #11819B;
  --gc-bg-main6: #FD6543;
  --gc-bg-main7: #093037;
  --gc-bg-main8: #F8CE69;
  --gc-bg-main9: #0D2B5E;
  --gc-bg-main10: #2CBCA5;
  --gc-bg-main11: #0587FF;
  --gc-bg-main12: #6729F9;
  --gc-bg-main13: #3546A3;
  --gc-bg-common-1: #F4F4F4;
  --gc-bg-common-2: #232323;
  --gc-bg-common-3: #F6F8FA;
  --gc-bg-common-4: #DFE9FB;
  --gc-bg-common-5: #F4F4F4;
  --gc-bg-common-6: #D0DFFF;
  --gc-bg-common-7: #E8EFFF;
  --gc-bg-common-8: #F1F8FF;
  --gc-bg-common-9: #E7F2F5;
  --gc-bg-common-10: #C8E0E7;
  --gc-bg-common-11: #FFF0EC;
  --gc-bg-common-12: #F6F7EE;
  --gc-bg-common-13: #F2F2F2;
  --gc-bg-common-14: rgba(9, 11, 14, 0.0941176471);
  --gc-bg-common-15: #EAF8F6;
  --gc-bg-common-16: #BDC3C9;
  --gc-bg-common-17: #F3FAFA;
  --gc-bg-common-18: #EDF2FB;
  --gc-bg-common-19: #F1F1F1;
  --gc-bg-common-20: rgba(249, 41, 41, 0.0352941176);
  --gc-bg-common-21: #fff2f2;
  --gc-bg-common-22: rgba(53, 69, 163, 0.0823529412);
  --gc-bg-white: #ffffff;
  --gc-bg-white10per: rgba(255, 255, 255, 0.1);
  --gc-bg-white20per: rgba(255, 255, 255, 0.199);
  --gc-bg-white30per: rgba(255, 255, 255, 0.308);
  --gc-border-1: #DDE1E9;
  --gc-border-2: #DDE0E5;
  --gc-border-3: #CFCFD0;
  --gc-border-4: #D0D1D1;
  --gc-border-5: #C5C9CF;
  --gc-border-6: rgba(61, 76, 94, 0.2);
  --gc-border-7: #D0D7D7;
  --gc-border-8: #E6E8E8;
  --f-fw-normal: 400;
  --f-fw-medium: 500;
  --f-fw-semibold: 600;
  --f-fw-bold: 700;
  --f-fw-ex-bold: 800;
  --f-ff-font-1: 'Inter', sans-serif;
  --f-fs-font-16: 16px;
  --f-fs-font-18: 18px;
  --f-fs-font-20: 20px;
  --f-fs-font-22: 22px;
  --f-fs-font-24: 24px;
  --f-fs-font-26: 26px;
  --f-fs-font-28: 28px;
  --f-fs-font-30: 30px;
  --f-fs-font-32: 32px;
  --f-fs-font-34: 34px;
  --f-fs-font-36: 36px;
  --f-fs-font-38: 38px;
  --f-fs-font-40: 40px;
  --f-fs-font-42: 42px;
  --f-fs-font-44: 44px;
  --f-fs-font-48: 48px;
  --f-fs-font-50: 50px;
  --f-fs-font-52: 52px;
  --f-fs-font-54: 54px;
  --f-fs-font-56: 56px;
  --f-fs-font-58: 58px;
  --f-fs-font-60: 60px;
  --f-fs-font-62: 62px;
  --f-fs-font-64: 64px;
  --f-fs-font-66: 66px;
  --f-fs-font-68: 68px;
  --f-fs-font-70: 70px;
  --f-fs-font-72: 72px;
  --f-fs-font-74: 74px;
  --f-fs-font-76: 76px;
  --f-fs-font-78: 78px;
  --f-fs-font-80: 80px;
  --f-fs-font-82: 82px;
  --f-fs-font-84: 84px;
  --f-fs-font-86: 86px;
  --f-fs-font-88: 88px;
}

table {
  width: 100%;
}

.auto-container {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  max-width: 1650px;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-right: auto;
  margin-left: auto;
}

.clients-sections .slick-slide {
  text-align: center;
  margin: 10px 5px;
  padding: 15px 10px;
  border: 1px solid #d9d9d9;
}
.clients-sections .slick-slide:focus {
  outline: none;
}
.clients-sections .slick-slide img {
  cursor: pointer;
}
.clients-sections .slick-slide img:hover {
  filter: none;
}
.clients-sections .slick-prev,
.clients-sections .slick-next {
  display: none !important;
}

/*
::::::::::::::::::::::::::
 COMMON AREA CSS
::::::::::::::::::::::::::
*/
.white-heading .sub-title {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
  display: inline-block;
  margin-bottom: 16px;
}
.white-heading .sub-title img {
  transform: translateY(-2px);
  margin-right: 3px;
  filter: brightness(0) invert(1);
}
.white-heading h2 {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-44);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-44); /* 100% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .white-heading h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
@media (max-width: 767px) {
  .white-heading h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
.white-heading p {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-28); /* 155.556% */
}
.white-heading h5 {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-32);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-38); /* 118.75% */
}
.white-heading h3 {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-52);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-52); /* 118.75% */
}

.heading1 .sub-title {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
  display: inline-block;
  margin-bottom: 16px;
}
.heading1 .sub-title img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.heading1 h2 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-44);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-52); /* 118.182% */
}
.heading1 h2 span {
  color: var(--gc-bg-main);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .heading1 h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
@media (max-width: 767px) {
  .heading1 h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
.heading1 h3 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-32);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-38); /* 118.75% */
}
.heading1 h5 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 130% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading1 h5 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main3);
}
.heading1 h4 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-26); /* 130% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading1 h4 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main3);
}
.heading1 p {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-26); /* 144.444% */
}

.heading2 .sub-title {
  color: var(--gc-bg-main4);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
  display: inline-block;
  margin-bottom: 16px;
}
.heading2 .sub-title img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.heading2 h2 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-44);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-52); /* 118.182% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .heading2 h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
@media (max-width: 767px) {
  .heading2 h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
.heading2 h3 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-32);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-38); /* 118.75% */
}
.heading2 h5 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 130% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading2 h5 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main4);
}
.heading2 h4 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-26); /* 130% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading2 h4 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main4);
}
.heading2 h5 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-32); /* 130% */
  text-transform: capitalize;
}
.heading2 h5 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-32); /* 130% */
  text-transform: capitalize;
}
.heading2 h5 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main4);
}
.heading2 p {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-26); /* 144.444% */
}

.white-heading h2 {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-44);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-44); /* 100% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .white-heading h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
@media (max-width: 767px) {
  .white-heading h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
.white-heading p {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-28); /* 155.556% */
}
.white-heading h5 {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-32);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-38); /* 118.75% */
}
.white-heading h3 {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-52);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-52); /* 118.75% */
}

.heading4 .sub-title {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
  display: inline-block;
  margin-bottom: 16px;
}
.heading4 .sub-title img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.heading4 h2 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-44);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-52); /* 118.182% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .heading4 h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
@media (max-width: 767px) {
  .heading4 h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
.heading4 h3 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-32);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-38); /* 118.75% */
}
.heading4 h3 a {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-32);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-38); /* 118.75% */
  display: inline-block;
  transition: all 0.4s;
}
.heading4 h3 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main6);
}
.heading4 h5 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-32); /* 130% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading4 h5 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main6);
}
.heading4 h4 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-26); /* 130% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading4 h4 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main6);
}
.heading4 p {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-26); /* 144.444% */
  transition: all 0.4s;
}

.heading5 .sub-title {
  color: var(--gc-bg-main7);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
  display: inline-block;
  margin-bottom: 16px;
}
.heading5 .sub-title img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.heading5 h2 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-44);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-52); /* 118.182% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .heading5 h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
@media (max-width: 767px) {
  .heading5 h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
.heading5 h3 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-32);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-38); /* 118.75% */
}
.heading5 h5 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 130% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading5 h5 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main7);
}
.heading5 h4 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-26); /* 130% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading5 h4 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main7);
}
.heading5 p {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-26); /* 144.444% */
}
.heading8 .sub-title2 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
  display: inline-block;
  margin-bottom: 16px;
  background-color: var(--gc-bg-white);
  padding: 8px 12px 4px 12px;
  border-radius: 8px;
}
.heading8 .sub-title2 img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.heading8 h2 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-44);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-52); /* 118.182% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .heading8 h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
@media (max-width: 767px) {
  .heading8 h2 {
    font-size: var(--f-fs-font-32);
    line-height: var(--f-fs-font-40);
  }
}
.heading8 h3 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-32);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-38); /* 118.75% */
}
.heading8 h3 a {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-32);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-38); /* 100% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading8 h3 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main10);
}
.heading8 h5 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-32); /* 130% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading8 h5 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main10);
}
.heading8 h4 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-30); /* 130% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.heading8 h4 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main11);
}
.heading8 p {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-26); /* 144.444% */
  transition: all 0.4s;
}

/*============================
++++PAGE-PROGRESS-SATRT+++++
=============================*/
/* video button  */
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}

.border-radius {
  border-radius: 12px;
}

/*
::::::::::::::::::::::::::
 COMMON AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BUTTONS AREA CSS
::::::::::::::::::::::::::
*/
.theme-btn1 {
  display: inline-block;
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
  padding: 17px 24px;
  border-radius: 12px;
  background-color: var(--gc-bg-main1);
  transition: all 0.4s;
  border: none;
  position: relative;
  z-index: 2;
  overflow: hidden;
}
.theme-btn1::after {
  content: "";
  position: absolute;
  top: 0;
  left: -1px;
  height: 100%;
  width: 104%;
  background-color: var(--gc-bg-main);
  z-index: -1;
  transform: rotateY(90deg);
  transition: all 0.4s;
  animation-timing-function: linear;
  border-radius: 12px;
}
.theme-btn1:hover {
  color: var(--gc-text-white);
  transition: all 0.4s;
}
.theme-btn1:hover::after {
  transform: rotateY(0deg);
  transition: all 0.4s;
  animation-timing-function: linear;
}

.theme-btn3 {
  display: inline-block;
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
  padding: 17px 10px 17px 22px;
  border-radius: 52px;
  background-color: var(--gc-bg-main);
  transition: all 0.4s;
  border: none;
  position: relative;
  z-index: 2;
  overflow: hidden;
}
.theme-btn3 span {
  display: inline-block;
  transform: rotate(-45deg) translateX(0px);
  font-size: 17px;
}
.theme-btn3 .arrow2 {
  transform: translateY(-8px) rotate(-45deg) translateX(-52px);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn3 .arrow1 {
  transition: all 0.4s;
  opacity: 1;
}
.theme-btn3:hover .arrow2 {
  transform: translateY(-15px) rotate(-45deg) translateX(-21px);
  transition: all 0.4s;
  opacity: 1;
}
.theme-btn3:hover .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}
.theme-btn3::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: var(--gc-bg-main);
  border-radius: 25px;
  z-index: -1;
  transform: rotateY(90deg);
  transition: all 0.4s;
  animation-timing-function: linear;
}
.theme-btn3:hover {
  color: var(--gc-text-white);
  transition: all 0.4s;
}
.theme-btn3:hover::after {
  transform: rotateY(0deg);
  transition: all 0.4s;
  animation-timing-function: linear;
}

.theme-btn7 {
  display: inline-block;
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
  padding: 17px 22px 17px 22px;
  border-radius: 12px;
  background-color: var(--gc-bg-main);
  transition: all 0.4s;
  border: none;
  position: relative;
  z-index: 2;
  overflow: hidden;
}
.theme-btn7::after {
  content: "";
  position: absolute;
  top: 0;
  left: auto;
  right: 0;
  height: 100%;
  width: 0%;
  background-color: var(--gc-text-title1);
  border-radius: 12px;
  z-index: -1;
  transition: all 0.4s;
  animation-timing-function: linear;
}
.theme-btn7:hover {
  color: var(--gc-text-white);
  transition: all 0.4s;
}
.theme-btn7:hover::after {
  transition: all 0.4s;
  width: 100%;
  left: 0;
  right: auto;
  animation-timing-function: linear;
}

.theme-btn8__text {
  position: relative;
  z-index: 2;
}

.theme-btn8__shape {
  display: inline-block;
  position: absolute;
  width: 25%;
  height: 100%;
  background: var(--gc-text-title1);
  transform: translateY(150%);
  border-radius: 50%;
  left: calc((var(--b) - 1) * 25%);
  transition: 0.5s;
  transition-delay: calc((var(--b) - 1) * 0.1s);
  z-index: 1;
  color: var(--gc-text-white);
}

.theme-btn8__shape:nth-child(1) {
  --b: 1;
}

.theme-btn8__shape:nth-child(2) {
  --b: 2;
}

.theme-btn8__shape:nth-child(3) {
  --b: 3;
}

.theme-btn8__shape:nth-child(4) {
  --b: 4;
}

/*
 ::::::::::::::::::::::::::
     BUTOTNS AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 CTA AREA CSS
::::::::::::::::::::::::::
*/

.cta2-form {
  margin: 0px 130px;
  padding-top: 40px;
  position: relative;
}
@media (max-width: 767px) {
  .cta2-form {
    margin: 0px 20px;
  }
}
.cta2-form input {
  width: 100%;
  border: 1px solid var(--gc-bg-white10per);
  border-radius: 111px;
  background-color: var(--gc-bg-white10per);
  padding: 16px;
  color: var(--gc-text-white);
  margin-right: 140px;
}
.cta2-form input::-moz-placeholder {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16); /* 100% */
  text-transform: capitalize;
}
.cta2-form input::placeholder {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16); /* 100% */
  text-transform: capitalize;
}
.cta2-form input:focus {
  outline-color: var(--gc-bg-main4);
  outline-width: 1px;
  outline-style: solid;
}
.cta2-form input:focus::-moz-placeholder {
  opacity: 0;
}
.cta2-form input:focus::placeholder {
  opacity: 0;
}
.cta2-form .button {
  position: absolute;
  top: 41px;
  right: 2px;
}

/*
 ::::::::::::::::::::::::::
  CTA AREA CSS
 ::::::::::::::::::::::::::
 */
/*
 ::::::::::::::::::::::::::
  HERO AREA CSS
 ::::::::::::::::::::::::::
 */
.banner-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.banner-video #gc-banner-video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  transform: translate(-50%, -50%);
  -o-object-fit: cover;
     object-fit: cover;
  z-index: -1;
}
.banner-video #gc-banner-video:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  /* Black overlay with 50% opacity */
  z-index: 0;
  /* Sits above the video but below the content */
}

.pieChartwrapper {
  display: flex;
  align-items: flex-end;
  gap: 30px;
  float: right;
  position: relative;
  bottom: -50px;
}
.pieChartwrapper .main-counter {
  margin-bottom: 30px;
}
.pieChartwrapper .main-counter .percent {
  font-size: 60px;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 30px;
}
.pieChartwrapper .main-counter .label {
  line-height: 35px;
  margin-top: 10px;
  font-size: 30px;
  text-transform: uppercase;
  font-weight: bold;
  color: #ffffff;
}
.pieChartwrapper .pieBarscontainer {
  position: relative;
  width: 440px;
  height: 440px;
  transform: rotate(-150deg);
}
.pieChartwrapper .pieBarscontainer .circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}
.pieChartwrapper .pieBarscontainer svg {
  transform: rotate(90deg);
}
.pieChartwrapper .counters {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.pieChartwrapper .counters .label {
  font-size: 16px;
  margin-bottom: 20px;
  color: #fff;
}
.pieChartwrapper .counters .label span {
  display: inline-block;
  width: 64px;
  font-size: 30px;
  font-weight: bold;
  margin-right: 15px;
}

.hero1 {
  min-height: calc(100vh - 170px);
  background-position: center top;
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.main-heading1 {
  padding-right: 40px;
  padding-top: 90px;
  position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .main-heading1 {
    padding-top: 180px;
  }
}
@media (max-width: 767px) {
  .main-heading1 {
    padding-top: 180px;
  }
}
.main-heading1 span.sub-title {
  display: inline-block;
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18);
  /* 100% */
  margin-bottom: 16px;
}
.main-heading1 span.sub-title img {
  transform: translateY(-2px);
  margin-right: 2px;
}
.main-heading1 h1 {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-64);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: 74px;
  /* 115.625% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .main-heading1 h1 {
    font-size: var(--f-fs-font-40);
    line-height: var(--f-fs-font-48);
  }
}
@media (max-width: 767px) {
  .main-heading1 h1 {
    font-size: var(--f-fs-font-40);
    line-height: var(--f-fs-font-48);
  }
}
.main-heading1 p {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-28);
  /* 155.556% */
  text-transform: capitalize;
}
.main-heading1 .form-area {
  margin-top: 30px;
  position: relative;
  max-width: 700px;
}
.main-heading1 .form-area input {
  padding: 16px;
  width: 100%;
  border-radius: 12px;
  background-color: var(--gc-bg-white);
  border: 1px solid var(--gc-bg-white10per);
  color: var(--gc-bg-common-2);
}
.main-heading1 .form-area input::-moz-placeholder {
  color: var(--gc-bg-common-2);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16);
  /* 100% */
  text-transform: capitalize;
}
.main-heading1 .form-area input::placeholder {
  color: var(--gc-bg-common-2);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16);
  /* 100% */
  text-transform: capitalize;
}
.main-heading1 .form-area input:focus {
  outline-color: var(--gc-bg-main);
  outline-width: 1px;
  outline-style: solid;
}
.main-heading1 .form-area input:focus::-moz-placeholder {
  opacity: 0;
}
.main-heading1 .form-area input:focus::placeholder {
  opacity: 0;
}
.main-heading1 .form-area .button {
  position: absolute;
  top: 2px;
  right: 4px;
}

.hero4 {
  background: #202020;
  min-height: 900px;
  background-position: center top;
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;
  position: relative;
  z-index: 2;
}

.main-heading4 {
  padding-right: 40px;
  padding-top: 180px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .main-heading4 {
    padding-top: 180px;
  }
}
@media (max-width: 767px) {
  .main-heading4 {
    padding-top: 180px;
  }
}
.main-heading4 span.sub-title {
  display: inline-block;
  color: var(--gc-bg-main1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18);
  /* 100% */
  margin-bottom: 16px;
}
.main-heading4 span.sub-title img {
  transform: translateY(-2px);
  margin-right: 2px;
}
.main-heading4 h1 {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-56);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-64);
  /* 115.625% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .main-heading4 h1 {
    font-size: var(--f-fs-font-40);
    line-height: var(--f-fs-font-48);
  }
}
@media (max-width: 767px) {
  .main-heading4 h1 {
    font-size: var(--f-fs-font-40);
    line-height: var(--f-fs-font-48);
  }
}
.main-heading4 p {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-28);
  /* 155.556% */
  text-transform: capitalize;
}
.main-heading4 .form-area {
  margin-top: 30px;
  position: relative;
  margin-left: 100px;
  margin-right: 100px;
}
.main-heading4 .form-area .single-input {
  margin-right: 180px;
}
.main-heading4 .form-area input {
  padding: 16px;
  width: 100%;
  border-radius: 111px;
  background-color: var(--gc-bg-common-2);
  border: 1px solid var(--gc-bg-white10per);
  color: var(--gc-text-white);
}
.main-heading4 .form-area input::-moz-placeholder {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16);
  /* 100% */
  text-transform: capitalize;
}
.main-heading4 .form-area input::placeholder {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16);
  /* 100% */
  text-transform: capitalize;
}
.main-heading4 .form-area input:focus {
  outline-color: var(--gc-bg-main6);
  outline-width: 1px;
  outline-style: solid;
}
.main-heading4 .form-area input:focus::-moz-placeholder {
  opacity: 0;
}
.main-heading4 .form-area input:focus::placeholder {
  opacity: 0;
}
.main-heading4 .form-area .button {
  position: absolute;
  top: 0;
  right: 0;
}

.hero4-images {
  margin-top: 80px;
}
.hero4-images .image1 {
  margin-top: 82px;
}
.hero4-images .image3 {
  margin-top: 60px;
}
.hero4-images .image4 {
  transform: scale(1.1) translateX(-5px);
}
@media (max-width: 767px) {
  .hero4-images .image4 {
    margin-top: 70px;
  }
}
.hero4-images .image5 {
  margin-top: 75px;
}
@media (max-width: 767px) {
  .hero4-images .image5 {
    margin-top: 40px;
    margin-bottom: 40px;
  }
}

.about5-images .image img {
  width: 100%;
}
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translatex(-100%);
  }
}
@keyframes marquee-2 {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translatex(0);
  }
}

.inner-hero {
  min-height: 410px;
  display: flex;
  align-items: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}
.inner-hero .inner-main-heading {
  padding-top: 80px;
}
.inner-hero .inner-main-heading h1 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-56);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-56);
  /* 100% */
}
@media (max-width: 767px) {
  .inner-hero .inner-main-heading h1 {
    font-size: var(--f-fs-font-40);
    line-height: var(--f-fs-font-40);
  }
}
.inner-hero .inner-main-heading .breadcrumbs-pages {
  padding-top: 12px;
}
.inner-hero .inner-main-heading .breadcrumbs-pages ul li {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-16);
  /* 100% */
  display: inline-block;
}
.inner-hero .inner-main-heading .breadcrumbs-pages ul li.angle {
  padding: 0px 3px;
}
.inner-hero .inner-main-heading .breadcrumbs-pages ul li a {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16);
  /* 100% */
  transition: all 0.4s;
}
.inner-hero .inner-main-heading .breadcrumbs-pages ul li a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main11);
}

/*
::::::::::::::::::::::::::
 PRELOADER AREA CSS
::::::::::::::::::::::::::
*/
@keyframes progress {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes typing {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
@keyframes progress {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes typing {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
@keyframes progress {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes typing {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
@keyframes progress {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes typing {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
@keyframes progress {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes typing {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
@keyframes progress {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes typing {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
@keyframes progress {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes typing {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
@keyframes progress {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes typing {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
@keyframes progress {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes typing {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}

/*
 ::::::::::::::::::::::::::
  PRELOADER AREA CSS
 ::::::::::::::::::::::::::
 */
/*
 ::::::::::::::::::::::::::
  BACKGROUNDS AREA CSS
 ::::::::::::::::::::::::::
 */
.sec-bg1 {
  background-color: var(--gc-bg-common-1);
}

.sec-bg2 {
  background-color: var(--gc-bg-common-3);
}

.sec-bg3 {
  background-color: var(--gc-bg-common-5);
}

.sec-bg6 {
  background-color: var(--gc-text-title1);
}

/*
::::::::::::::::::::::::::
 BACKGROUNDS AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  ANIMATIONS AREA CSS
 ::::::::::::::::::::::::::
 */
@keyframes round-circle {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(1000deg);
  }
}
@keyframes round-circle2 {
  0% {
    transform: rotate(-20deg);
  }
  100% {
    transform: rotate(20deg);
  }
}
@keyframes animate1 {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-20px);
  }
}
@keyframes animate2 {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(20px);
  }
}
@keyframes animate3 {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(15px);
  }
}
@keyframes animate4 {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-25px);
  }
}
@keyframes animate5 {
  0% {
    transform: scale(0.5);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes animate6 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}
@keyframes animate7 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-70px);
  }
}
@keyframes animate8 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(70px);
  }
}

.animate1 {
  position: relative;
  animation-name: animate1;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.animate2 {
  position: relative;
  animation-name: animate2;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.animate3 {
  position: relative;
  animation-name: animate3;
  animation-duration: 4s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.animate4 {
  position: relative;
  animation-name: animate4;
  animation-duration: 4s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.image-anime {
  overflow: hidden;
}

.image-anime:after {
  content: "";
  position: absolute;
  width: 200%;
  height: 0%;
  left: 50%;
  top: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%) rotate(-45deg);
  z-index: 1;
}

.image-anime:hover:after {
  height: 250%;
  transition: all 600ms linear;
  background-color: transparent;
}

/*
::::::::::::::::::::::::::
 ANIMATION AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  PAGINATION AREA CSS
 ::::::::::::::::::::::::::
 */

/*
::::::::::::::::::::::::::
 PAGINATION AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  SEARCH AREA CSS
 ::::::::::::::::::::::::::
 */

/*
::::::::::::::::::::::::::
 SEARCH AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  THEME SETTINGS
 ::::::::::::::::::::::::::
 */
/* #Progress
================================================== */
.progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  transition: all 200ms linear;
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  z-index: 999;
}

.progress-wrap::after {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  color: var(--gc-text-title1);
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
}

.progress-wrap:hover::after {
  opacity: 0;
}

.progress-wrap::before {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  opacity: 0;
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 2;
  transition: all 200ms linear;
}

.progress-wrap:hover::before {
  opacity: 1;
}

.progress-wrap svg path {
  fill: none;
}

.progress-wrap svg.progress-circle path {
  stroke: var(--gc-text-title1); /* --- Lijn progres kleur --- */
  stroke-width: 4;
  box-sizing: border-box;
  transition: all 200ms linear;
}

/*
 ::::::::::::::::::::::::::
   THEME SETTINGS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 BLOG AREA CSS
::::::::::::::::::::::::::
*/
.sidebar-area {
  position: sticky;
  top: 140px;
  left: 0;
}

.blog-details-content .image img {
  width: 100%;
}
.blog-details-content .vl-blog12-meta a {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
  display: inline-block;
  margin-right: 20px;
}
.blog-details-content .vl-blog12-meta a img {
  transform: translateY(-2px);
}
.blog-details-content .heading2 h3 {
  font-weight: var(--f-fw-medium);
}

/*
::::::::::::::::::::::::::
 BLOG AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BLOG SIDEBAR AREA CSS
::::::::::::::::::::::::::
*/
.sidebar-area ._sidebar-widget {
  background-color: var(--gc-bg-common-1);
  padding: 28px 24px;
  border-radius: 8px;
}
.sidebar-area ._sidebar-widget h3 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 100% */
  margin-bottom: 24px;
}
.sidebar-area ._sidebar-widget._list ul li a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--gc-text-title-1);
  font-size: var(--f-fs-font-18);
  line-height: var(--f-fs-font-18);
  font-weight: var(--f-fw-medium);
  transition: all 0.4s;
  background-color: var(--gc-bg-white);
  padding: 20px 24px;
  margin-bottom: 20px;
  border-radius: 111px;
}
.sidebar-area ._sidebar-widget._list ul li a:hover {
  background-color: var(--gc-bg-main4);
  transition: all 0.4s;
  color: var(--gc-bg-white);
}
.sidebar-area ._sidebar-widget._list ul li a.active {
  background-color: var(--gc-bg-main4);
  transition: all 0.4s;
  color: var(--gc-bg-white);
}
.sidebar-area ._sidebar-widget._portfolio .portfolio-list ul li {
  color: #090B0E;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 18px; /* 100% */
  text-transform: capitalize;
  padding: 24px 0px;
  border-bottom: 1px solid rgba(61, 76, 94, 0.2);
}
.sidebar-area ._sidebar-widget._portfolio .portfolio-list ul li span {
  display: inline-block;
  color: #3D4C5E;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 100% */
  text-transform: capitalize;
  padding-left: 16px;
}
.sidebar-area ._sidebar-widget._portfolio .portfolio-list ul li:nth-last-child(1) {
  border-bottom: none;
}
.sidebar-area ._sidebar-widget._portfolio .protfolio-icon-list {
  margin-top: 4px;
}
.sidebar-area ._sidebar-widget._portfolio .protfolio-icon-list a {
  display: inline-block;
  height: 36px;
  width: 36px;
  line-height: 36px;
  text-align: center;
  font-size: 16px;
  color: var(--gc-text-title1);
  background-color: rgba(21, 95, 255, 0.1);
  margin-right: 5px;
  border-radius: 50%;
  transition: all 0.4s;
}
.sidebar-area ._sidebar-widget._portfolio .protfolio-icon-list a:hover {
  transition: all 0.4s;
  color: var(--gc-text-white);
  background-color: var(--gc-bg-main4);
}
.sidebar-area ._sidebar-widget._buttons a {
  width: 100%;
  padding: 16px;
  border-radius: 111px;
  display: inline-block;
  text-align: center;
  margin-top: 16px;
  transition: all 0.4s;
}
.sidebar-area ._sidebar-widget._buttons a img {
  transform: translateY(-2px);
}
.sidebar-area ._sidebar-widget._buttons a:hover {
  transition: all 0.4s;
  transform: translateY(-5px);
}
.sidebar-area ._sidebar-widget._buttons .sidebar-btn1 {
  background-color: var(--gc-bg-main4);
  color: var(--gc-text-white);
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 100% */
}
.sidebar-area ._sidebar-widget._buttons .sidebar-btn2 {
  border-radius: 100px;
  border: 1px solid rgba(52, 52, 52, 0.1);
  background: var(--gc-bg-white);
  color: var(--gc-text-title1);
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 100% */
}
.sidebar-area ._sidebar-widget._tags .tags-list ul li {
  display: inline-block;
}
.sidebar-area ._sidebar-widget._tags .tags-list ul li a {
  display: inline-block;
  background-color: var(--gc-bg-white);
  padding: 10px 14px;
  border-radius: 111px;
  color: var(--gc-text-title-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
  text-transform: capitalize;
  transition: all 0.4s;
  margin-right: 10px;
  margin-top: 14px;
}
.sidebar-area ._sidebar-widget._tags .tags-list ul li a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-white);
  background-color: var(--gc-bg-main4);
}
.sidebar-area ._sidebar-widget._contact ._contact-form input, .sidebar-area ._sidebar-widget._contact ._contact-form textarea {
  width: 100%;
  margin-top: 20px;
  border: 1px solid rgba(52, 52, 52, 0.1);
  background-color: var(--gc-bg-white);
  border-radius: 8px;
  padding: 16px;
}
.sidebar-area ._sidebar-widget._contact ._contact-form input:focus, .sidebar-area ._sidebar-widget._contact ._contact-form textarea:focus {
  outline-color: var(--gc-bg-main4);
  outline-width: 1px;
  outline-style: solid;
}
.sidebar-area ._sidebar-widget._contact ._contact-form input:focus::-moz-placeholder, .sidebar-area ._sidebar-widget._contact ._contact-form textarea:focus::-moz-placeholder {
  opacity: 0;
}
.sidebar-area ._sidebar-widget._contact ._contact-form input:focus::placeholder, .sidebar-area ._sidebar-widget._contact ._contact-form textarea:focus::placeholder {
  opacity: 0;
}
.sidebar-area ._sidebar-widget._contact ._contact-form input::-moz-placeholder, .sidebar-area ._sidebar-widget._contact ._contact-form textarea::-moz-placeholder {
  color: var(--gc-text-title-1);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 100% */
  opacity: 0.7;
}
.sidebar-area ._sidebar-widget._contact ._contact-form input::placeholder, .sidebar-area ._sidebar-widget._contact ._contact-form textarea::placeholder {
  color: var(--gc-text-title-1);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 100% */
  opacity: 0.7;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post {
  display: flex;
  align-items: center;
  margin-top: 28px;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .image {
  width: 86px;
  border-radius: 8px;
  overflow: hidden;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .image img {
  width: 86px;
  transition: all 0.4s;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content {
  padding-left: 20px;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content a.date {
  display: inline-block;
  color: var(--gc-text-title-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-16); /* 100% */
  text-transform: uppercase;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content a.date img {
  transform: translateY(-3px);
  margin-right: 3px;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content h4 a {
  display: inline-block;
  color: var(--gc-text-title-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-24); /* 133.333% */
  text-transform: capitalize;
  padding-top: 12px;
  transition: all 0.4s;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content h4 a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main4);
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post:hover .image img {
  transition: all 0.4s;
  transform: scale(1.1) rotate(2deg);
}

/*
::::::::::::::::::::::::::
 BLOG SIDEBAR AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BLOG POST BOX AREA CSS
::::::::::::::::::::::::::
*/

.vl-blog-8-item {
  background-color: var(--gc-bg-white);
  border-radius: 8px;
  transition: all 0.4s;
}
.vl-blog-8-item .vl-blog8-meta a {
  color: #0C0723;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 18px; /* 100% */
  display: inline-block;
}
.vl-blog-8-item .vl-blog8-meta a img {
  transform: translateY(-2px);
  margin-right: 2px;
}
.vl-blog-8-item .vl-blog-8-thumb {
  border-radius: 8px 8px 0px 0px;
  overflow: hidden;
  position: relative;
}
.vl-blog-8-item .vl-blog-8-thumb img {
  transition: all 0.4s;
}
.vl-blog-8-item .vl-blog-8-thumb .user {
  display: inline-block;
  position: absolute;
  bottom: 16px;
  left: 20px;
  background-color: var(--gc-bg-white);
  padding: 8px 12px 5px 12px;
  border-radius: 8px;
}
.vl-blog-8-item .vl-blog-8-content {
  padding: 24px;
}
.vl-blog-8-item .vl-blog-8-content .learn8 {
  display: inline-block;
  overflow: hidden;
  padding-top: 10px;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-16); /* 100% */
  transition: all 0.4s;
  position: relative;
  text-align: center;
  text-transform: uppercase;
  z-index: 34;
  border-radius: 0px 0px 8px 8px;
}
.vl-blog-8-item .vl-blog-8-content .learn8 span {
  display: inline-block;
  transform: rotate(-45deg) translateX(0px) translateY(1px);
  font-size: 17px;
  transition: all 0.4s;
}
.vl-blog-8-item .vl-blog-8-content .learn8 .arrow2 {
  transform: translateY(-8px) rotate(-45deg) translateX(-52px);
  transition: all 0.4s;
  opacity: 0;
}
.vl-blog-8-item .vl-blog-8-content .learn8 .arrow1 {
  transition: all 0.4s;
  opacity: 1;
}
.vl-blog-8-item .vl-blog-8-content .learn8:hover {
  color: var(--gc-bg-main11);
  transition: all 0.4s;
}
.vl-blog-8-item .vl-blog-8-content .learn8:hover .arrow2 {
  transform: translateY(-12px) rotate(-45deg) translateX(-18px);
  transition: all 0.4s;
  opacity: 1;
}
.vl-blog-8-item .vl-blog-8-content .learn8:hover .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}
.vl-blog-8-item:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.vl-blog-8-item:hover .vl-blog-8-thumb img {
  transition: all 0.4s;
  transform: scale(1.1) rotate(2deg);
}

/*
::::::::::::::::::::::::::
 BLOG POST BOX AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BLOG RECENT POST AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BLOG RECENT POST AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 1 CSS
::::::::::::::::::::::::::
*/
.vl-footer-area1 {
  background-position: center bottom;
  background-size: cover;
  background-repeat: no-repeat;
}
.vl-footer-area1 .footer-bottom-content {
  padding-top: 50px;
  margin-top: 40px;
  border-top: 1px solid var(--gc-bg-white30per);
}

.vl-footer-widget-white h4 {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 100% */
  text-transform: capitalize;
  padding-bottom: 24px;
}
.vl-footer-widget-white .vl-footer-list ul li a {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16); /* 100% */
  padding: 10px 0px;
  display: inline-block;
  transition: all 0.4s;
}
.vl-footer-widget-white .vl-footer-list ul li a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main1);
  transform: translateX(5px);
}

/*
::::::::::::::::::::::::::
 FOOTER AREA 1 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 2 CSS
::::::::::::::::::::::::::
*/
.vl-footer-social2 a {
  display: inline-block;
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  background-color: var(--gc-bg-white10per);
  border: 1px solid var(--gc-bg-white10per);
  border-radius: 50%;
  margin: 0px 3px;
  color: var(--gc-text-white);
  transition: all 0.4s;
}
.vl-footer-social2 a:hover {
  transition: all 0.4s;
  background-color: var(--gc-bg-main);
  transform: translateY(-5px);
  color: var(--gc-bg-white);
}

.vl-footer-widget-white2 h4 {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 100% */
  text-transform: capitalize;
  padding-bottom: 24px;
}
.vl-footer-widget-white2 .vl-footer-list ul li a {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16); /* 100% */
  padding: 10px 0px;
  display: inline-block;
  transition: all 0.4s;
}
.vl-footer-widget-white2 .vl-footer-list ul li a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main);
  transform: translateX(5px);
}

.vl-footer-contact2 .single-contact-item {
  display: flex;
  align-items: start;
  padding: 10px 0px;
}
.vl-footer-contact2 .single-contact-item .text a {
  display: inline-block;
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-26); /* 162.5% */
  padding-left: 10px;
  transition: all 0.4s;
}
.vl-footer-contact2 .single-contact-item .text a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main);
}

.vl-copyright2 {
  padding: 22px 0px;
}
.vl-copyright2._dv-top {
  border-top: 1px solid var(--gc-bg-white30per);
}
.vl-copyright2 .copyright-text p, .vl-copyright2 .copyright-text a {
  display: inline-block;
  color: var(--gc-text-white70per);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-18); /* 100% */
  position: relative;
}
.vl-copyright2 .copyright-text p.add-before, .vl-copyright2 .copyright-text a.add-before {
  padding-left: 40px;
}
.vl-copyright2 .copyright-text p.add-before::after, .vl-copyright2 .copyright-text a.add-before::after {
  content: "";
  position: absolute;
  top: 0;
  left: 15px;
  height: 18px;
  width: 2px;
  background-color: var(--gc-bg-white30per);
}
.vl-copyright2 .copyright-text a {
  transition: all 0.4s;
}
.vl-copyright2 .copyright-text a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-copyright2 .copyright-text.right-side {
    padding-top: 20px;
  }
}
@media (max-width: 767px) {
  .vl-copyright2 .copyright-text.right-side {
    padding-top: 20px;
  }
}

/*
::::::::::::::::::::::::::
 FOOTER AREA 2 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 3 CSS
::::::::::::::::::::::::::
*/

.vl-footer-widget-black1 h4 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 100% */
  text-transform: capitalize;
  padding-bottom: 24px;
}
.vl-footer-widget-black1 .vl-footer-list ul li a {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16); /* 100% */
  padding: 10px 0px;
  display: inline-block;
  transition: all 0.4s;
}
.vl-footer-widget-black1 .vl-footer-list ul li a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main5);
  transform: translateX(5px);
}

.vl-footer-contact3 .single-contact-item {
  display: flex;
  align-items: start;
  padding: 10px 0px;
}
.vl-footer-contact3 .single-contact-item img {
  filter: brightness(0);
}
.vl-footer-contact3 .single-contact-item .text a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-26); /* 162.5% */
  padding-left: 10px;
  transition: all 0.4s;
}
.vl-footer-contact3 .single-contact-item .text a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main5);
}

/*
::::::::::::::::::::::::::
 FOOTER AREA 3 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 2 CSS
::::::::::::::::::::::::::
*/

/*
::::::::::::::::::::::::::
 FOOTER AREA 2 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 5 CSS
::::::::::::::::::::::::::
*/

/*
::::::::::::::::::::::::::
 FOOTER AREA 5 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 6 CSS
::::::::::::::::::::::::::
*/

/*
::::::::::::::::::::::::::
 FOOTER AREA 6 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 7 CSS
::::::::::::::::::::::::::
*/

/*
::::::::::::::::::::::::::
 FOOTER AREA 7 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 7 CSS
::::::::::::::::::::::::::
*/

/*
::::::::::::::::::::::::::
 FOOTER AREA 8 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 9 CSS
::::::::::::::::::::::::::
*/

/*
::::::::::::::::::::::::::
 FOOTER AREA 9 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 7 CSS
::::::::::::::::::::::::::
*/

/*
::::::::::::::::::::::::::
 FOOTER AREA 8 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 1 CSS
::::::::::::::::::::::::::
*/
.vl-logo {
  max-width: 340px;
}

.header-tranperent {
  position: absolute;
  top: 0;
  z-index: 999;
  width: 100%;
}

/*
::::::::::::::::::::::::::
 HEADER AREA 1 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 2 CSS
::::::::::::::::::::::::::
*/

/*
::::::::::::::::::::::::::
 HEADER AREA 2 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 3 CSS
::::::::::::::::::::::::::
*/

/*
::::::::::::::::::::::::::
 HEADER AREA 3 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 4 CSS
::::::::::::::::::::::::::
*/

.vl-header8-btns .buttons {
  display: flex;
  align-items: center;
  justify-content: end;
}

/*
::::::::::::::::::::::::::
 HEADER AREA 4 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 5 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 5 CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  NAV MENU 1 CSS
 ::::::::::::::::::::::::::
 */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area1 {
    top: 16px;
  }
}
@media (max-width: 767px) {
  .vl-header-area1 {
    top: 16px;
  }
}
.vl-header-area1 .vl-main-menu {
  padding: 16px 24px;
  display: inline-block;
  margin: auto;
  text-align: end;
}
.vl-header-area1 .vl-main-menu ul {
  text-align: center;
}
.vl-header-area1 .vl-main-menu ul > li {
  display: inline-block;
  position: relative;
}
.vl-header-area1 .vl-main-menu ul > li > a {
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-18);
  line-height: var(--f-fs-font-18);
  display: inline-block;
  position: relative;
  transition: 0.4s;
  padding: 0 16px;
}
.vl-header-area1 .vl-main-menu ul > li > a.active {
  color: var(--gc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li > a span {
  transform: translateY(3px);
  display: inline-block;
}
.vl-header-area1 .vl-main-menu ul > li > a:hover {
  color: var(--gc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li:hover a {
  color: var(--gc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu {
  position: absolute;
  top: 201%;
  width: 220px;
  left: 0;
  background: #fff;
  padding: 12px 20px 8px;
  opacity: 0;
  visibility: hidden;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  transition: 0.4s;
  border-radius: 4px;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--gc-bg-main1);
  top: 0;
  left: 0;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li {
  margin-right: 0;
  display: block;
  text-align: start;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li a {
  color: var(--gc-text-title1);
  display: inline-block;
  font-size: var(--f-fs-font-18);
  line-height: var(--f-fs-font-18);
  position: relative;
  z-index: 1;
  padding: 12px 0;
  font-weight: var(--f-fw-normal);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li a::after {
  position: absolute;
  content: "";
  height: 45px;
  width: 0px;
  transition: all 0.4s;
  left: -20px;
  top: 2px;
  right: auto;
  z-index: 1;
  background-color: var(--gc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li a:hover::after {
  width: 2px;
  transition: all 0.4s;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li a:before {
  display: none;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li .sub-menu {
  left: 100%;
  top: 201%;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li:hover > a {
  color: var(--gc-bg-main1);
  padding-left: 4px;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li:hover > a::after {
  width: 3px;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
}
.vl-header-area1 .vl-main-menu ul > li:hover a {
  color: var(--gc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
  transition: all 0.4s;
}
.vl-header-area1 .vl-main-menu ul > li:hover .vl-mega-menu {
  opacity: 1;
  visibility: visible;
  transition: 0.3s;
  top: 201%;
  transform: scale(1);
  max-height: 500px;
  overflow: hidden;
  overflow-y: scroll;
}
.vl-header-area1 .vl-mega-menu {
  position: absolute;
  left: -409px;
  top: 100px;
  width: 1298px;
  background: #fff;
  padding: 25px;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
  top: 201.3%;
  transform: scale(1, 0);
  transform-origin: top;
  border-radius: 0px 0px 5px 5px;
  max-height: 500px;
  overflow: hidden;
  overflow-y: scroll;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .vl-header-area1 .vl-mega-menu {
    width: 1140px;
    left: -300px;
  }
}
.vl-header-area1 .vl-mega-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--gc-bg-main1);
  top: 0;
  left: 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .vl-header-area1 .vl-mega-menu {
    left: -162px;
    width: 929px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .vl-header-area1 .vl-mega-menu {
    width: auto;
    opacity: 1;
    visibility: visible;
    transition: none;
    position: static;
    display: none;
    transform: scale(1);
  }
}

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 1 CSS
 ::::::::::::::::::::::::::
 */
/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/
.vl-header-area1 .vl-header-action-item {
  float: right;
  padding: 6px;
  border-radius: 4px;
}
.vl-header-area1 .vl-header-action-item button {
  border: 1px solid var(--gc-text-white);
  outline: none;
  background: none;
  transition: all 0.4s;
  color: var(--gc-text-white);
  font-size: var(--f-fs-font-22);
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 8px;
  font-size: var(--f-fs-font-22);
}

.vl-offcanvas {
  position: fixed;
  background: var(--gc-bg-white);
  width: 450px;
  z-index: 9999;
  right: 0;
  top: 0;
  padding: 50px 40px;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transform: translateX(100%);
  transition: 0.3s;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
}
@media only screen and (max-width: 450px) {
  .vl-offcanvas {
    width: 100%;
  }
}
.vl-offcanvas-close-toggle {
  font-size: var(--f-fs-font-24);
  color: var(--gc-text-title-1);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .vl-offcanvas-header {
    margin-bottom: 40px;
  }
}
.vl-offcanvas-social a {
  display: inline-block;
  text-align: center;
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 40px;
  color: var(--gc-text-title1);
  border: 1px solid var(--gc-text-title1);
  font-size: 14px;
}
.vl-offcanvas-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 50;
  width: 100%;
  height: 100%;
  visibility: hidden;
  opacity: 0;
  transition: 0.45 easc-in-out;
  background: rgba(24, 24, 24, 0.4);
}
.vl-offcanvas .vl-offcanvas-logo {
  height: 50px;
  width: 122px;
  -o-object-fit: contain;
     object-fit: contain;
}
.vl-offcanvas .vl-offcanvas-close button {
  border: none;
  background: none;
  outline: none;
  color: var(--gc-text-title1);
}
.vl-offcanvas .vl-offcanvas-social h4 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  text-transform: capitalize;
  padding-bottom: 24px;
}
.vl-offcanvas .vl-offcanvas-social a {
  color: var(--gc-text-title1);
  margin-right: 4px;
}

.vl-offcanvas-menu ul {
  list-style: none;
}
.vl-offcanvas-menu ul li {
  position: relative;
}
.vl-offcanvas-menu ul li a {
  padding: 8px 0;
  display: block;
  font-size: var(--f-fs-font-18);
  font-weight: var(--f-fw-medium);
  color: var(--gc-text-title1);
  transition: all 0.4s;
}
.vl-offcanvas-menu ul li a span {
  display: none;
}
.vl-offcanvas-menu ul li > a {
  border-bottom: none;
}
.vl-offcanvas-menu ul li.active > a {
  color: var(--gc-text-title1);
}
.vl-offcanvas-menu ul li .sub-menu {
  display: none;
  padding-left: 20px;
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 1 CSS
 ::::::::::::::::::::::::::
 */

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 1 CSS
 ::::::::::::::::::::::::::
 */
/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/

/*
 ::::::::::::::::::::::::::
  NAV MENU 3 CSS
 ::::::::::::::::::::::::::
 */

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 3 CSS
 ::::::::::::::::::::::::::
 */
/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/

/*
 ::::::::::::::::::::::::::
  NAV MENU 4 CSS
 ::::::::::::::::::::::::::
 */

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 4 CSS
 ::::::::::::::::::::::::::
 */
/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/

/*
 ::::::::::::::::::::::::::
  NAV MENU 5 CSS
 ::::::::::::::::::::::::::
 */

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 5 CSS
 ::::::::::::::::::::::::::
 */
/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/

/*
 ::::::::::::::::::::::::::
  NAV MENU 5 CSS
 ::::::::::::::::::::::::::
 */

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 5 CSS
 ::::::::::::::::::::::::::
 */
/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/

/*
 ::::::::::::::::::::::::::
  NAV MENU 7 CSS
 ::::::::::::::::::::::::::
 */

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 7 CSS
 ::::::::::::::::::::::::::
 */
/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/

/*
 ::::::::::::::::::::::::::
  NAV MENU 7 CSS
 ::::::::::::::::::::::::::
 */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area8 {
    top: 0;
    background-color: var(--gc-bg-white);
    padding: 8px 0px;
  }
}
@media (max-width: 767px) {
  .vl-header-area8 {
    top: 0;
    background-color: var(--gc-bg-white);
    padding: 8px 0px;
  }
}
.vl-header-area8 .header2-bg {
  margin-top: 20px;
  border-radius: 16px;
  padding: 16px 24px;
  background-color: var(--gc-bg-white);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area8 .header2-bg {
    background-color: transparent;
    margin-top: 0;
    border-radius: 0;
    padding: 0px 0px;
  }
}
@media (max-width: 767px) {
  .vl-header-area8 .header2-bg {
    background-color: transparent;
    margin-top: 0;
    border-radius: 0;
    padding: 0px 0px;
  }
}
.vl-header-area8 .vl-main-menu {
  padding: 16px 24px;
  display: inline-block;
  margin: auto;
  text-align: end;
}
.vl-header-area8 .vl-main-menu ul {
  text-align: center;
}
.vl-header-area8 .vl-main-menu ul > li {
  display: inline-block;
  position: relative;
}
.vl-header-area8 .vl-main-menu ul > li > a {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-18);
  line-height: var(--f-fs-font-18);
  display: inline-block;
  position: relative;
  transition: 0.4s;
  padding: 0 16px;
  font-weight: 500;
}
.vl-header-area8 .vl-main-menu ul > li > a.active {
  color: var(--gc-bg-main);
}
.vl-header-area8 .vl-main-menu ul > li > a span {
  transform: translateY(3px);
  display: inline-block;
}
.vl-header-area8 .vl-main-menu ul > li > a:hover {
  color: var(--gc-bg-main);
}
.vl-header-area8 .vl-main-menu ul > li:hover a {
  color: var(--gc-bg-main);
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu {
  position: absolute;
  top: 201%;
  width: 220px;
  left: 0;
  background: #fff;
  padding: 12px 20px 8px;
  opacity: 0;
  visibility: hidden;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  transition: 0.4s;
  border-radius: 4px;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--gc-bg-main);
  top: 0;
  left: 0;
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu li {
  margin-right: 0;
  display: block;
  text-align: start;
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu li a {
  color: var(--gc-text-title1);
  display: inline-block;
  font-size: var(--f-fs-font-18);
  line-height: var(--f-fs-font-18);
  position: relative;
  z-index: 1;
  padding: 12px 0;
  font-weight: var(--f-fw-normal);
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu li a::after {
  position: absolute;
  content: "";
  height: 45px;
  width: 0px;
  transition: all 0.4s;
  left: -20px;
  top: 2px;
  right: auto;
  z-index: 1;
  background-color: var(--gc-bg-main);
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu li a:hover::after {
  width: 2px;
  transition: all 0.4s;
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu li a:before {
  display: none;
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu li .sub-menu {
  left: 100%;
  top: 201%;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu li:hover > a {
  color: var(--gc-bg-main);
  padding-left: 4px;
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu li:hover > a::after {
  width: 3px;
}
.vl-header-area8 .vl-main-menu ul > li .sub-menu li:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
}
.vl-header-area8 .vl-main-menu ul > li:hover a {
  color: var(--gc-bg-main);
}
.vl-header-area8 .vl-main-menu ul > li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
  transition: all 0.4s;
}
.vl-header-area8 .vl-main-menu ul > li:hover .vl-mega-menu {
  opacity: 1;
  visibility: visible;
  transition: 0.3s;
  top: 201%;
  transform: scale(1);
  max-height: 500px;
  overflow: hidden;
}
.vl-header-area8 .vl-mega-menu {
  position: absolute;
  left: -605px;
  top: 100px;
  width: 1188px;
  background: #fff;
  padding: 25px;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
  top: 201.3%;
  transform: scale(1, 0);
  transform-origin: top;
  border-radius: 0px 0px 5px 5px;
  max-height: 500px;
  overflow: hidden;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .vl-header-area8 .vl-mega-menu {
    width: 1140px;
    left: -300px;
  }
}
.vl-header-area8 .vl-mega-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--gc-bg-main);
  top: 0;
  left: 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .vl-header-area8 .vl-mega-menu {
    left: -162px;
    width: 929px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .vl-header-area8 .vl-mega-menu {
    width: auto;
    opacity: 1;
    visibility: visible;
    transition: none;
    position: static;
    display: none;
    transform: scale(1);
  }
}
.vl-header-area8 .vl-mega-menu .vl-home-menu .mega-menu-col {
  text-align: left;
}
.vl-header-area8 .vl-mega-menu .vl-home-menu .mega-menu-col .menu-title {
  font-size: var(--f-fs-font-18);
  line-height: var(--f-fs-font-18);
  color: var(--gc-text-title1);
  margin-bottom: 12px;
  font-weight: bold;
}
.vl-header-area8 .vl-mega-menu .vl-home-menu .mega-menu-col ul {
  text-align: left;
}
.vl-header-area8 .vl-mega-menu .vl-home-menu .mega-menu-col ul li {
  margin-bottom: 8px;
  width: 100%;
}
.vl-header-area8 .vl-mega-menu .vl-home-menu .mega-menu-col ul li a {
  display: block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-16);
  line-height: var(--f-fs-font-16);
  position: relative;
  transition: 0.4s;
  width: 100%;
  padding: 6px 15px;
  border-radius: 5px;
  min-height: 45px;
}
.vl-header-area8 .vl-mega-menu .vl-home-menu .mega-menu-col ul li a i {
  color: #ffffff;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  text-align: center;
  background-color: #ec2024;
  line-height: 25px;
  margin-top: 2px;
  margin-bottom: 2px;
  font-size: 14px;
  margin-right: 7px;
}
.vl-header-area8 .vl-mega-menu .vl-home-menu .mega-menu-col ul li a:hover {
  color: var(--gc-bg-main);
  background: #f5f5f5;
}
.vl-header-area8 .vl-mega-menu .vl-home-menu .mega-menu-col ul li a span {
  display: block;
  font-size: 13px;
  color: #6d6d6d;
}
.vl-header-area8 .vl-mega-menu .vl-home-menu .mega-menu-col .mega-menu-one-line-text a {
  padding: 12px 15px;
}
.vl-header-area8 .vl-mega-menu .vl-home-menu .mega-menu-col .mega-menu-two-line-text a {
  height: 58px;
}

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 7 CSS
 ::::::::::::::::::::::::::
 */
/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/
.vl-header-area8 .vl-header-action-item {
  float: right;
  padding: 6px;
  border-radius: 4px;
}
.vl-header-area8 .vl-header-action-item button {
  border: 1px solid var(--gc-text-title1);
  outline: none;
  background: none;
  transition: all 0.4s;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-22);
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 8px;
  font-size: var(--f-fs-font-22);
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 7 CSS
 ::::::::::::::::::::::::::
 */

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 7 CSS
 ::::::::::::::::::::::::::
 */
/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/

/*
 ::::::::::::::::::::::::::
  NAV MENU 10 CSS
 ::::::::::::::::::::::::::
 */

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 10 CSS
 ::::::::::::::::::::::::::
 */
/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/

.about-experience-area {
  padding-top: 100px;
}
.about-experience-area .about-experience-content i {
  height: 80px;
  width: 80px;
  text-align: center;
  line-height: 80px;
  background: #ec0203;
  color: #fff;
  font-size: 30px;
  border-radius: 50% 50% 0 50%;
}
.about-experience-area .about-experience-content .title {
  font-size: 60px;
  font-weight: 500;
  padding-top: 35px;
  padding-bottom: 27px;
}
.about-experience-area .about-experience-content p {
  font-size: 30px;
  line-height: 40px;
  padding: 0 60px;
}
.about-experience-area .experience-item {
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.1);
  padding: 42px 0;
  border-radius: 10px;
  transition: all 0.3s ease-out 0s;
}
.about-experience-area .experience-item i {
  font-size: 40px;
  color: #1f1f1f;
  transition: all 0.3s ease-out 0s;
}
.about-experience-area .experience-item p {
  font-weight: 500;
  padding-top: 8px;
  transition: all 0.3s ease-out 0s;
}
.about-experience-area .experience-item:hover {
  background: #727272;
}
.about-experience-area .experience-item:hover i {
  color: #fff;
}
.about-experience-area .experience-item:hover p {
  color: #fff;
}
.about-experience-area .mt-80 {
  margin-top: 80px;
}
.about-experience-area .mt-140 {
  margin-top: 140px;
}
.about-experience-area .mt-5 {
  margin-top: 3rem !important;
}

/*
 ::::::::::::::::::::::::::
  ABOUT AREA CSS
 ::::::::::::::::::::::::::
 */

.about2-images {
  height: 570px;
  position: relative;
}
.about2-images .image1 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
}
.about2-images .image2 {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 1;
  width: 600px;
  border-radius: 10px;
}

@media (max-width: 767px) {
  .counter span {
    font-size: 40px;
  }
  .testimonial-content p {
    font-size: 20px;
    line-height: 32px;
  }
  .portfolio-list {
    padding-left: 0;
  }
}
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translatex(-100%);
  }
}
@keyframes marquee-2 {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translatex(0);
  }
}

/*
 ::::::::::::::::::::::::::
  ABOUT AREA CSS
 ::::::::::::::::::::::::::
 */
/*
 ::::::::::::::::::::::::::
  SERVICE AREA CSS
 ::::::::::::::::::::::::::
*/
.service1-image {
  border-radius: 8px;
  height: 332px;
  right: 0;
}
@media (max-width: 767px) {
  .service1-image {
    height: auto;
  }
}
.service1-image img {
  width: 100%;
  transition: all 0.4s;
  -o-object-fit: cover;
     object-fit: cover;
}

.service1-box {
  padding: 20px 20px 20px 16px;
  border-radius: 8px;
  background-color: var(--gc-bg-white);
  display: flex;
  align-items: start;
  transition: all 0.4s;
}
.service1-box .num {
  height: 34px;
  width: 34px;
  text-align: center;
  line-height: 34px;
  border-radius: 50%;
  background-color: var(--gc-bg-common-1);
  transition: all 0.4s;
}
.service1-box .num p {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  text-transform: capitalize;
  margin: 0;
  line-height: 34px;
}
.service1-box .heading1 {
  padding-left: 16px;
}
.service1-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.08);
}
.service1-box:hover .num {
  background-color: var(--gc-bg-main1);
  transition: all 0.4s;
}
.service1-box:hover .num p {
  color: var(--gc-text-white);
}

.service2-box {
  height: 370px;
  overflow: hidden;
  border-radius: 8px;
}
.service2-box .image img {
  width: 100%;
}
.service2-box .with-content {
  position: relative;
  padding: 30px;
  z-index: 2;
}
.service2-box .with-content .learn {
  display: inline-block;
  overflow: hidden;
  padding-top: 16px;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-16); /* 100% */
  transition: all 0.4s;
}
.service2-box .with-content .learn span {
  display: inline-block;
  transform: rotate(-45deg) translateX(0px) translateY(1px);
  font-size: 17px;
  transition: all 0.4s;
}
.service2-box .with-content .learn .arrow2 {
  transform: translateY(-8px) rotate(-45deg) translateX(-52px);
  transition: all 0.4s;
  opacity: 0;
}
.service2-box .with-content .learn .arrow1 {
  transition: all 0.4s;
  opacity: 1;
}
.service2-box .with-content .learn:hover {
  color: var(--gc-bg-main4);
  transition: all 0.4s;
}
.service2-box .with-content .learn:hover .arrow2 {
  transform: translateY(-12px) rotate(-45deg) translateX(-18px);
  transition: all 0.4s;
  opacity: 1;
}
.service2-box .with-content .learn:hover .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}
.service2-box.content-box {
  display: flex;
  align-items: end;
  z-index: 2;
  position: relative;
}
.service2-box.content-box::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-image: url(../img/bg/hero2-bg.jpg);
  z-index: -3;
  transform: scale(1.4);
  transition: all 0.4s;
}
.service2-box.content-box::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: var(--gc-bg-common-4);
  transition: all 0.4s;
  z-index: -2;
}
.service2-box.content-box:hover {
  transition: all 0.4s;
}
.service2-box.content-box:hover::before {
  transform: scale(1);
  transition: all 0.4s;
}
.service2-box.content-box:hover::after {
  opacity: 0;
  transition: all 0.4s;
}
.service2-box.content-box:hover .learn {
  color: var(--gc-bg-main4);
  transition: all 0.4s;
}
.service2-box.content-box:hover .learn .arrow2 {
  transform: translateY(-12px) rotate(-45deg) translateX(-18px);
  transition: all 0.4s;
  opacity: 1;
}
.service2-box.content-box:hover .learn .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.service5 {
  position: relative;
  z-index: 1;
}
.service5 .service-widgets-section {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.service5 .service-widgets-section .tab-content .tab-pane {
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: var(--gc-bg-white);
  -webkit-backdrop-filter: blur(15px);
          backdrop-filter: blur(15px);
  padding: 40px 50px;
  transform: rotateX(45deg) translateY(50px);
  transition: all 0.4s;
  opacity: 0;
  overflow: hidden;
}
@media (max-width: 767px) {
  .service5 .service-widgets-section .tab-content .tab-pane {
    padding: 24px;
  }
}
.service5 .service-widgets-section .tab-content .tab-pane.fade.show.active {
  transform: rotateX(0deg) translateY(0);
  opacity: 1;
}
.service5 .service-widgets-section .tab-content .tab-pane .service-boxarea .icons {
  background: #f7f7f7;
  border: 1px solid #d7d7d7;
  display: inline-block;
  transition: all 0.4s;
  border-radius: 12px;
  height: 90px;
  width: 90px;
  text-align: center;
  line-height: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.service5 .service-widgets-section .tab-content .tab-pane .service-boxarea .icons i {
  font-size: 50px;
  color: var(--gc-bg-main);
}
.service5 .service-widgets-section .tab-content .tab-pane .service-boxarea .content-area h3 {
  color: var(--gc-text-title-7);
  font-size: 28px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px; /* 100% */
  display: inline-block;
}
.service5 .service-widgets-section .tab-content .tab-pane .service-boxarea .content-area p {
  color: var(--gc-text-pera-7);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-26); /* 144.444% */
  padding-top: 20px;
}
.service5 .service-widgets-section .tab-content .tab-pane .images-area {
  position: relative;
}
@media (max-width: 767px) {
  .service5 .service-widgets-section .tab-content .tab-pane .images-area {
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .service5 .service-widgets-section .tab-content .tab-pane .images-area {
    margin-top: 30px;
  }
}
.service5 .service-widgets-section .tab-content .tab-pane .images-area .img1 img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.service5 .service-widgets-section .tabs-btn-area {
  position: relative;
  z-index: 1;
}
.service5 .service-widgets-section .tabs-btn-area::after {
  position: absolute;
  content: "";
  height: 10px;
  width: 100%;
  background: var(--gc-bg-white);
  top: -32px;
  left: 0;
  right: 0;
  transition: all 0.4s;
  border-radius: 30px;
}
.service5 .service-widgets-section .tabs-btn-area ul {
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 767px) {
  .service5 .service-widgets-section .tabs-btn-area ul {
    justify-content: center;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .service5 .service-widgets-section .tabs-btn-area ul li:nth-child(4) {
    margin-top: 20px;
  }
}
.service5 .service-widgets-section .tabs-btn-area ul li button {
  border-radius: 12px;
  background: var(--gc-bg-white);
  border: 1px solid rgba(5, 135, 255, 0.1);
  padding: 8px 16px 8px 8px;
  position: relative;
  z-index: 1;
  color: var(--gc-text-title-7);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
  display: flex;
  align-items: center;
}
@media (max-width: 767px) {
  .service5 .service-widgets-section .tabs-btn-area ul li button {
    display: block !important;
    margin-bottom: 16px;
  }
}
.service5 .service-widgets-section .tabs-btn-area ul li button::after {
  position: absolute;
  z-index: 1;
  content: "";
  height: 10px;
  left: 0;
  top: -32px;
  transition: all 0.4s;
  width: 100%;
  background: var(--gc-bg-main2);
  border-radius: 12px;
  visibility: hidden;
  opacity: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .service5 .service-widgets-section .tabs-btn-area ul li button::after {
    display: none;
  }
}
@media (max-width: 767px) {
  .service5 .service-widgets-section .tabs-btn-area ul li button::after {
    display: none;
  }
}
.service5 .service-widgets-section .tabs-btn-area ul li button.active {
  background: var(--gc-bg-main2);
  color: var(--gc-bg-white);
}
.service5 .service-widgets-section .tabs-btn-area ul li button.active::after {
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
}
.service5 .service-widgets-section .tabs-btn-area ul li button.active span {
  background: rgb(255, 255, 255);
  border: 1px solid rgb(255, 255, 255);
}
.service5 .service-widgets-section .tabs-btn-area ul li button.active span img {
  transition: all 0.4s;
  filter: brightness(40);
}
.service5 .service-widgets-section .tabs-btn-area ul li button span {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  border-radius: 8px;
  transition: all 0.4s;
  display: inline-block;
  background: #f7f7f7;
  border: 1px solid #d7d7d7;
  margin: 0 8px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.service5 .service-widgets-section .tabs-btn-area ul li button span i {
  font-size: 20px;
  color: var(--gc-bg-main);
}

/*
 ::::::::::::::::::::::::::
  SERVICE AREA CSS
 ::::::::::::::::::::::::::
 */
/*
 ::::::::::::::::::::::::::
  FAQ AREA CSS
 ::::::::::::::::::::::::::
*/

.faq5-area .accordion-button::after {
  filter: brightness(0);
}
.faq5-area .accordion {
  border: none;
}
.faq5-area .accordion .accordion-item {
  margin-top: 20px;
  border: none;
  border-radius: 7px;
  padding: 0;
  border: none;
  border: 1px solid var(--gc-border-3);
  background: transparent;
}
.faq5-area .accordion .accordion-item h2 {
  font-size: var(--f-fs-font-20);
  line-height: var(--f-fs-font-20);
  font-weight: var(--f-fw-semibold);
  color: var(--gc-text-title1);
}
.faq5-area .accordion .accordion-item button {
  font-size: var(--f-fs-font-20);
  line-height: var(--f-fs-font-20);
  font-weight: var(--f-fw-semibold);
  color: var(--gc-text-title1);
  background: transparent;
  padding: 20px;
}
.faq5-area .accordion .accordion-item button:focus {
  box-shadow: none;
}
.faq5-area .accordion .accordion-item .accordion-body {
  color: var(--gc-text-white80per);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-26); /* 162.5% */
  padding: 0px 16px 16px 22px;
  background-color: var(--gc-bg-main2);
}
.faq5-area .accordion .accordion-item.active {
  background-color: var(--gc-bg-main2);
}
.faq5-area .accordion .accordion-item.active button {
  color: var(--gc-text-white);
}
.faq5-area .accordion .accordion-item.active button::after {
  filter: brightness(0) invert(1);
}
.faq5-area .accordion .accordion-button:not(.collapsed)::after {
  background-image: var(--bs-accordion-btn-active-icon);
  transform: var(--bs-accordion-btn-icon-transform);
  filter: brightness(0) invert(1);
}
.faq5-area .accordion-button:not(.collapsed) {
  color: var(--ztc-text-text-1);
  background-color: var(--gc-bg-main2);
  border: none;
  box-shadow: inset 0 calc(var(--bs-accordion-border-width) * -1) 0 var(--bs-accordion-border-color);
}
.faq5-area .accordion {
  --bs-accordion-color: #000;
  --bs-accordion-bg: #fff;
  --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  --bs-accordion-border-color: #dee2e600;
  --bs-accordion-border-width: 1px;
  --bs-accordion-border-radius: 0.375rem;
  --bs-accordion-inner-border-radius: calc(0.375rem - 1px);
  --bs-accordion-btn-padding-x: 1.25rem;
  --bs-accordion-btn-padding-y: 1rem;
  --bs-accordion-btn-color: var(--bs-body-color);
  --bs-accordion-btn-bg: var(--bs-accordion-bg);
  --bs-accordion-btn-icon: url(data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="var%28--bs-body-color%29"%3e%3cpath fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/%3e%3c/svg%3e);
  --bs-accordion-btn-icon-width: 1.25rem;
  --bs-accordion-btn-icon-transform: rotate(-180deg);
  --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
  --bs-accordion-btn-active-icon: url(data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%230c63e4"%3e%3cpath fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/%3e%3c/svg%3e);
  --bs-accordion-btn-focus-border-color: #86b7fe;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  --bs-accordion-body-padding-x: 1.25rem;
  --bs-accordion-body-padding-y: 1rem;
  --bs-accordion-active-color: #0c63e4;
  --bs-accordion-active-bg: #e7f1ff;
}

/*
 ::::::::::::::::::::::::::
  FAQ AREA CSS
 ::::::::::::::::::::::::::
*/
.testimonial2-slider-area {
  position: relative;
  z-index: 1;
}
.testimonial2-slider-area .rev_slider .slick-slider {
  margin-left: -20%;
  margin-right: -20%;
}
.testimonial2-slider-area .rev_slider .slick-list {
  padding-top: 10% !important;
  padding-bottom: 6% !important;
  padding-left: 15% !important;
  padding-right: 15% !important;
}
.testimonial2-slider-area .rev_slider .slick-track {
  max-width: 100% !important;
  transform: translate3d(0, 0, 0) !important;
  perspective: 100px;
}
.testimonial2-slider-area .rev_slider .slick-slide {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  opacity: 0;
  width: 100% !important;
  transform: translate3d(0, 0, 0);
  transition: transform 1s, opacity 1s;
}
.testimonial2-slider-area .rev_slider .slick-snext,
.testimonial2-slider-area .rev_slider .slick-sprev {
  display: block;
}
.testimonial2-slider-area .rev_slider .slick-current {
  opacity: 1;
  position: relative;
  display: block;
  transform: translate3d(0, 0, 10px);
  z-index: 2;
}
.testimonial2-slider-area .rev_slider .slick-snext {
  opacity: 1;
  transform: translate3d(20%, 0, 0px);
  z-index: 1;
}
.testimonial2-slider-area .rev_slider .slick-sprev {
  opacity: 1;
  transform: translate3d(-20%, 0, 0px);
}
.testimonial2-slider-area .rev_slider .rev_slide {
  margin-left: 30px;
}
.testimonial2-slider-area .rev_slider .rev_slide.slick-center .text {
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
}
.testimonial2-slider-area .rev_slider .rev_slide .test {
  display: block;
  width: 100%;
  height: 520px;
  width: 460px;
}
.testimonial2-slider-area .rev_slider .rev_slide .test img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 8px;
}
.testimonial2-slider-area .rev_slider .rev_slide .text {
  text-align: center;
  margin-top: -30px;
  visibility: hidden;
  opacity: 0;
  transition: all 0.4s;
  margin-left: 21px;
  margin-right: 85px;
}
.testimonial2-slider-area .rev_slider .rev_slide .text p {
  color: var(--ztc-text-text-5);
  font-family: var(--ztc-family-font2);
  font-size: var(--ztc-font-size-font-s44);
  font-style: normal;
  font-weight: var(--ztc-weight-bold);
}
.testimonial2-slider-area .rev_slider .rev_slide .text p span {
  color: rgba(9, 11, 14, 0.4);
  transition: all 0.4s;
  display: inline-block;
}
.testimonial2-slider-area .rev_slider .slick-arrow {
  height: 70px;
  width: 70px;
  text-align: center;
  line-height: 66px;
  border-radius: 50%;
  transition: all 0.4s;
  box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.055);
  color: var(--gc-bg-main6);
  outline: none;
  font-size: var(--f-fs-font-28);
  background-color: var(--gc-text-white);
  border: none;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial2-slider-area .rev_slider .slick-arrow {
    display: none;
  }
}
@media (max-width: 767px) {
  .testimonial2-slider-area .rev_slider .slick-arrow {
    display: none;
  }
}
.testimonial2-slider-area .rev_slider .slick-arrow:hover {
  background: var(--gc-bg-main6);
  transition: all 0.4s;
  color: var(--gc-text-white);
}
.testimonial2-slider-area .rev_slider .slick-arrow.prev-next {
  position: absolute;
  left: -90px;
  top: 50%;
  margin-top: -62px;
}
.testimonial2-slider-area .rev_slider .slick-arrow.next-prev {
  position: absolute;
  right: -90px;
  top: 50%;
  margin-top: -62px;
}

/*
 ::::::::::::::::::::::::::
  CASE STUDY AREA CSS
 ::::::::::::::::::::::::::
 */
.case-study-content .case-study-logo-image {
  max-width: 340px;
  margin-bottom: 20px;
}
.case-study-content ul {
  margin-bottom: 1rem;
  margin-top: 0.5rem;
}
.case-study-content ul li {
  font-size: 1rem;
  line-height: 1.5rem;
  line-height: 1.5;
  list-style-type: none;
  margin-bottom: 0.5rem;
  margin-top: 0.5rem;
  padding-left: 30px;
  position: relative;
}
.case-study-content ul li::before {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='14' height='14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m14 7-7 6.97L0 7 7 .03 14 7Z' fill='%23EC2024'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  content: "";
  height: 14px;
  left: 0;
  position: absolute;
  top: calc(0.75em - 7px);
  width: 14px;
}
.case-study-content .case-study-challenge-section ul,
.case-study-content .case-study-solutions-section ul,
.case-study-content .case-study-results-section ul {
  background: #fff;
  padding: 20px;
  border-radius: 12px;
}

/*
::::::::::::::::::::::::::
 CHOOSE AREA CSS
::::::::::::::::::::::::::
*/
.choose1-box {
  padding: 20px 20px 20px 16px;
  border-radius: 8px;
  background-color: var(--gc-bg-common-1);
  display: flex;
  align-items: start;
  transition: all 0.4s;
}
.choose1-box .num {
  height: 34px;
  width: 34px;
  text-align: center;
  line-height: 34px;
  border-radius: 50%;
  background-color: var(--gc-bg-white);
  transition: all 0.4s;
}
.choose1-box .num p {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  text-transform: capitalize;
  margin: 0;
  line-height: 34px;
}
.choose1-box .heading1 {
  padding-left: 16px;
}
.choose1-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.08);
  background-color: var(--gc-bg-white);
}
.choose1-box:hover .num {
  background-color: var(--gc-bg-main1);
  transition: all 0.4s;
}
.choose1-box:hover .num p {
  color: var(--gc-text-white);
}

.choose1-images {
  position: relative;
}
.choose1-images .image {
  height: 370px;
  border-radius: 8px;
}
.choose1-images .image img {
  -o-object-fit: cover;
     object-fit: cover;
}
.choose1-images .shape {
  position: absolute;
  top: -40px;
  left: -40px;
}

.choose5-box {
  background-color: var(--gc-bg-white);
  border-radius: 8px;
  padding: 24px 24px 24px 24px;
  transition: all 0.4s;
}
.choose5-box .image img {
  width: 100%;
  filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.055)) drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.05));
}
.choose5-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}

.choose4-box {
  border-radius: 8px;
  border: 1px solid var(--gc-border-5);
  padding: 24px 20px;
  transition: all 0.4s;
  min-height: 172px;
}
.choose4-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
  background-color: var(--gc-bg-main2);
}
.choose4-box:hover h4 a {
  color: var(--gc-bg-white);
  transition: all 0.4;
}
.choose4-box:hover p {
  transition: all 0.4s;
  color: var(--gc-text-white80per);
}

.choose4-images {
  position: relative;
}
.choose4-images .image {
  border-radius: 10px;
}
.choose4-images .shape {
  position: absolute;
  top: -40px;
  right: -50px;
  height: 160px;
}

/*
 ::::::::::::::::::::::::::
  CHOOSE AREA CSS
 ::::::::::::::::::::::::::
 */
/*
 ::::::::::::::::::::::::::
  TEAM AREA CSS
 ::::::::::::::::::::::::::
 */

@keyframes round-circle {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(1000deg);
  }
}

/*
::::::::::::::::::::::::::
 TEAM AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  TESTIMONIAL AREA CSS
 ::::::::::::::::::::::::::
 */

.tes4-single-slider {
  border-radius: 8px;
  background: var(--gc-bg-white);
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.08);
  padding: 24px 16px;
  margin: 0px 20px;
  opacity: 0.8;
  transition: all 0.4s;
}
.tes4-single-slider .auhtor_thumb {
  margin-right: 30px;
}
.tes4-single-slider .auhtor_thumb img {
  width: 100%;
}
.tes4-single-slider .author_text h5 {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-28); /* 140% */
  text-transform: capitalize;
  padding-top: 16px;
}
.tes4-single-slider .author_text .content {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-26); /* 144.444% */
  text-transform: capitalize;
  padding-top: 16px;
}
.tes4-single-slider .author_text .info {
  padding-top: 24px;
}
.tes4-single-slider .author_text .info a {
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.tes4-single-slider .author_text .info a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main6);
}
.tes4-single-slider .author_text .info p {
  color: var(--gc-text-pera1);
  font-size: 12px;
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: 12px; /* 100% */
  text-transform: capitalize;
  padding-top: 6px;
}
.tes4-single-slider.slick-current.slick-active {
  opacity: 1;
  transition: all 0.4s;
}

.tes4-slider .slick-list {
  overflow: visible;
}

.tes4 .slick-dots {
  position: absolute;
  bottom: -40px;
  left: 50%;
  list-style-type: none;
  display: flex;
  align-items: center;
  margin-left: -48px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tes4 .slick-dots {
    bottom: -25px;
  }
}
@media (max-width: 767px) {
  .tes4 .slick-dots {
    bottom: -25px;
  }
}
.tes4 .slick-dots li {
  margin: 0 0.25rem;
}
.tes4 .slick-dots button {
  display: block;
  width: 8px;
  height: 8px;
  padding: 0;
  margin: 0px 5px;
  border: none;
  border-radius: 100%;
  background-color: var(--gc-bg-common-16);
  text-indent: -9999px;
  position: relative;
}
.tes4 .slick-dots button::after {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  height: 18px;
  width: 18px;
  border: 5px solid var(--gc-bg-common-16);
  border-radius: 50%;
  opacity: 0;
  transition: all 0.4s;
}
.tes4 .slick-dots li.slick-active button {
  background-color: var(--gc-text-title1);
}
.tes4 .slick-dots li.slick-active button::after {
  opacity: 1;
  transition: all 0.4s;
}

:root {
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.testimonials-section {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}
.testimonials-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
}
.testimonials-section .testimonial-card {
  padding: 48px;
  border-bottom: 1px solid #e2e3e4;
  position: relative;
  overflow: hidden;
}
.testimonials-section .testimonial-card .testimonial-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}
.testimonials-section .testimonial-card .company-logo-container {
  border: 3px solid var(--gc-bg-main2);
  margin-top: 25px;
  text-align: center;
  width: 260px;
  height: 100px;
  background: #ffffff;
  overflow: visible;
  position: relative;
}
.testimonials-section .testimonial-card .company-logo-container .company-logo-inner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 260px;
  height: 100px;
  background: #ffffff;
  border: 3px solid var(--gc-bg-main2);
  top: -15px;
  right: -15px;
}
.testimonials-section .testimonial-card .company-logo-container .company-logo {
  height: auto;
  max-width: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.testimonials-section .testimonial-card .view-case-study-btn {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
  text-decoration-line: underline;
  text-decoration-style: solid;
  -webkit-text-decoration-skip: ink;
          text-decoration-skip-ink: auto;
  margin-top: 30px;
}
.testimonials-section .testimonial-card .testimonial-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
}
.testimonials-section .testimonial-card .testimonial-header .star-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}
.testimonials-section .testimonial-card .testimonial-header .star-rating i {
  color: #ff8400;
  font-size: 1.45rem;
  transition: transform 0.2s ease;
}
.testimonials-section .testimonial-card:hover .star-rating i {
  transform: scale(1.1);
}
.testimonials-section .testimonial-card .testimonial-content {
  position: relative;
  margin-bottom: 20px;
}
.testimonials-section .testimonial-card .testimonial-content .quote-icon {
  position: absolute;
  top: -16px;
  left: -8px;
  font-size: 4rem;
  color: var(--gc-bg-main);
  opacity: 0.1;
  z-index: 0;
}
.testimonials-section .testimonial-card .testimonial-content .testimonial-text {
  font-size: 1.125rem;
  color: var(--text-secondary);
  line-height: 1.8;
  font-weight: 400;
  position: relative;
  z-index: 1;
}
.testimonials-section .testimonial-card .testimonial-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 15px;
}
.testimonials-section .testimonial-card .testimonial-footer .user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}
.testimonials-section .testimonial-card .testimonial-footer .user-info .user-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
  border: 3px solid var(--gc-bg-common-2);
  transition: transform 0.3s ease;
  position: relative;
  z-index: 1;
}
.testimonials-section .testimonial-card .testimonial-footer .user-info .user-details h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}
.testimonials-section .testimonial-card .testimonial-footer .user-info .user-details .user-designation {
  font-size: 0.95rem;
  color: var(--text-secondary);
  margin: 0;
}
.testimonials-section .testimonial-card .testimonial-footer .platform-info {
  height: 64px;
  width: 64px;
  padding: 10px;
  border-radius: 50px;
  border: 3px solid var(--gc-bg-common-4);
  transition: all 0.3s ease;
  background: var(--gc-bg-common-3);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: -30px;
}
.testimonials-section .testimonial-card .testimonial-footer .platform-info .platform-logo {
  width: 24px;
  height: 24px;
  -o-object-fit: contain;
     object-fit: contain;
}
.testimonials-section .cta-section {
  background: linear-gradient(135deg, var(--gc-bg-main2), var(--gc-bg-main2));
  border-radius: 12px;
  padding: 64px 48px;
  text-align: center;
  margin-top: 80px;
  position: relative;
  overflow: hidden;
}
.testimonials-section .cta-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}
.testimonials-section .cta-section .cta-content {
  position: relative;
  z-index: 1;
}
.testimonials-section .cta-section .cta-content .cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  line-height: 1.2;
}
.testimonials-section .cta-section .cta-content .cta-description {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 32px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}
.testimonials-section .cta-section .cta-content .cta-button {
  background: white;
  color: var(--gc-bg-main);
  border: none;
  padding: 16px 40px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.125rem;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-lg);
}
.testimonials-section .cta-section .cta-content .cta-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: var(--gc-bg-main);
}
@media (max-width: 768px) {
  .testimonials-section {
    padding: 60px 0;
  }
  .testimonials-section .testimonial-card {
    padding: 32px 24px;
  }
  .testimonials-section .testimonial-card .testimonial-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  .testimonials-section .testimonial-card .testimonial-footer {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  .testimonials-section .cta-section {
    padding: 48px 24px;
  }
  .testimonials-section .cta-section .cta-content .cta-title {
    font-size: 2rem;
  }
}
@media (max-width: 576px) {
  .testimonials-section .testimonial-card {
    padding: 24px 16px;
  }
  .testimonials-section .testimonial-card .testimonial-content .testimonial-title {
    font-size: 1.5rem;
  }
  .testimonials-section .testimonial-card .testimonial-content .testimonial-text {
    font-size: 1rem;
  }
}

/*
::::::::::::::::::::::::::
 WORK AREA CSS
::::::::::::::::::::::::::
*/

.project6-box {
  position: relative;
  transition: all 0.4s;
  border-radius: 10px;
  overflow: hidden;
}
.project6-box .content-box {
  position: absolute;
  bottom: 30px;
  margin: 0px 30px;
  background-color: var(--gc-bg-common-1);
  border-radius: 8px;
  padding: 24px;
  transform: translateY(50px) scale(0.5);
  opacity: 0;
  transition: all 0.4s;
}
.project6-box .content-box h4 a {
  display: inline-block;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-30); /* 125% */
}
.project6-box .content-box p {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-26); /* 144.444% */
  text-transform: capitalize;
}
.project6-box .learn {
  display: inline-block;
  overflow: hidden;
  padding-top: 16px;
  color: var(--gc-text-title1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-16); /* 100% */
  transition: all 0.4s;
}
.project6-box .learn span {
  display: inline-block;
  transform: rotate(-45deg) translateX(0px) translateY(1px);
  font-size: 17px;
  transition: all 0.4s;
}
.project6-box .learn .arrow2 {
  transform: translateY(-8px) rotate(-45deg) translateX(-52px);
  transition: all 0.4s;
  opacity: 0;
}
.project6-box .learn .arrow1 {
  transition: all 0.4s;
  opacity: 1;
}
.project6-box .learn:hover {
  color: var(--gc-text-title1);
  transition: all 0.4s;
}
.project6-box .learn:hover .arrow2 {
  transform: translateY(-12px) rotate(-45deg) translateX(-18px);
  transition: all 0.4s;
  opacity: 1;
}
.project6-box .learn:hover .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}
.project6-box:hover .content-box {
  transition: all 0.4s;
  transform: scale(1) translateY(0);
  opacity: 1;
}

/*
 ::::::::::::::::::::::::::
  WORK AREA CSS
 ::::::::::::::::::::::::::
 */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.work-process-section {
  background: #f9f9f9;
  padding: 80px 0;
  position: relative;
}
@media (max-width: 767.98px) {
  .work-process-section {
    padding: 60px 0;
  }
}

.sec-title h3 {
  font-size: 2.5rem;
  color: #333;
  font-weight: 700;
  margin-bottom: 20px;
}
@media (max-width: 991.98px) {
  .sec-title h3 {
    font-size: 2rem;
  }
}
@media (max-width: 767.98px) {
  .sec-title h3 {
    font-size: 1.8rem;
  }
}

.lead {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
}

.process-timeline {
  position: relative;
}
.process-timeline::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #ec0203, #ff4444);
  transform: translateX(-50%);
  border-radius: 2px;
}
@media (max-width: 991.98px) {
  .process-timeline::before {
    left: 30px;
  }
}

.process-step {
  margin-bottom: 60px;
  position: relative;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.6s ease forwards;
}
.process-step:last-child {
  margin-bottom: 0;
}
@media (max-width: 767.98px) {
  .process-step {
    margin-bottom: 40px;
  }
}
.process-step:nth-child(1) {
  animation-delay: 0.1s;
}
.process-step:nth-child(2) {
  animation-delay: 0.2s;
}
.process-step:nth-child(3) {
  animation-delay: 0.3s;
}
.process-step:nth-child(4) {
  animation-delay: 0.4s;
}
.process-step:nth-child(5) {
  animation-delay: 0.5s;
}
.process-step:nth-child(6) {
  animation-delay: 0.6s;
}
.process-step:nth-child(7) {
  animation-delay: 0.7s;
}

.process-connector {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: #fff;
  border: 4px solid #ec0203;
  border-radius: 50%;
  box-shadow: 0 0 0 6px #f9f9f9;
  z-index: 2;
}
@media (max-width: 991.98px) {
  .process-connector {
    left: 30px;
  }
}

.process-left,
.process-right {
  padding: 0 60px;
}
@media (max-width: 991.98px) {
  .process-left,
  .process-right {
    padding-left: 80px;
    padding-right: 20px;
  }
}
@media (max-width: 991.98px) {
  .process-left .process-box,
  .process-right .process-box {
    margin: 0;
  }
}

.process-left .process-box {
  margin-left: auto;
  margin-right: 0;
}
.process-left .process-box::after {
  content: "";
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 20px;
  height: 20px;
  background: #fff;
  border: 2px solid #ec0203;
  border-left: none;
  border-bottom: none;
  z-index: 1;
}
@media (max-width: 991.98px) {
  .process-left .process-box::after {
    display: none;
  }
}

.process-right .process-box {
  margin-left: 0;
  margin-right: auto;
}
.process-right .process-box::after {
  content: "";
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%) rotate(-135deg);
  width: 20px;
  height: 20px;
  background: #fff;
  border: 2px solid #ec0203;
  border-left: none;
  border-bottom: none;
  z-index: 1;
}
@media (max-width: 991.98px) {
  .process-right .process-box::after {
    display: none;
  }
}

.process-box {
  background: #fff;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}
.process-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: #ec0203;
}
@media (max-width: 767.98px) {
  .process-box {
    padding: 25px;
  }
}

.process-step-number {
  background: linear-gradient(135deg, #ec0203, #ff4444);
  color: #fff;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(236, 2, 3, 0.3);
  margin-bottom: 20px;
}
.process-step-number strong {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}
.process-step-number h2 {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  line-height: 1;
}
@media (max-width: 767.98px) {
  .process-step-number h2 {
    font-size: 24px;
  }
}
@media (max-width: 767.98px) {
  .process-step-number {
    width: 80px;
    height: 80px;
  }
}

.process-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
}
@media (max-width: 767.98px) {
  .process-title {
    font-size: 1.3rem;
  }
}

.process-description {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.process-icon {
  font-size: 2rem;
  color: #ec0203;
  margin-bottom: 20px;
}

/*
::::::::::::::::::::::::::
 others AREA CSS
::::::::::::::::::::::::::
*/

.brands9-area h3 {
  color: #0C0723;
  font-size: 32px;
  font-style: normal;
  font-weight: 500;
  line-height: 32px; /* 100% */
  letter-spacing: -0.64px;
}

.brands9-single {
  width: 213px;
  height: 90px;
  background-color: #fdfdfd;
  border: 1px solid #d7d7d7;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s;
}
.brands9-single:hover {
  transition: all 0.4s;
  border: 1px solid var(--gc-bg-main);
}

.categories-buttons {
  text-align: center;
}
.categories-buttons .nav li {
  display: inline-block;
  margin: 0px 5px;
  margin-bottom: 10px;
}
.categories-buttons .nav li a {
  color: #3D4C5E;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 100% */
  padding: 13px 20px;
  border-radius: 30px;
  background: var(--gc-bg-common-1);
  border-radius: 30px;
}
.categories-buttons .nav li a.active {
  background: var(--gc-bg-main2);
}

.portfolio-box {
  background-color: var(--gc-bg-common-1);
  border-radius: 8px;
  overflow: hidden;
  margin-top: 30px;
  transition: all 0.4s;
}
.portfolio-box .image-area {
  position: relative;
}
.portfolio-box .image-area .image {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}
.portfolio-box .image-area .image img {
  width: 100%;
}
.portfolio-box .image-area .image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgb(0, 0, 0);
  opacity: 0;
  transition: all 0.4s;
}
.portfolio-box .image-area .arrow {
  display: inline-block;
  height: 60px;
  width: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 18px;
  background-color: var(--gc-bg-main);
  border-radius: 50%;
  color: var(--gc-text-white);
  transform: rotate(-45deg);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -30px;
  margin-left: -30px;
  z-index: 3;
  transition: all 0.4s;
  transform: scale(1.5);
  opacity: 0;
}
.portfolio-box .image-area .arrow:hover {
  transition: all 0.4s;
  background-color: var(--gc-text-title1);
}
.portfolio-box .content-area {
  padding: 24px 24px 32px 24px;
}
.portfolio-box .content-area span {
  display: inline-block;
  color: var(--gc-text-pera1);
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 100% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.portfolio-box .content-area a {
  display: block;
  color: var(--gc-text-title1);
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 100% */
  padding-top: 20px;
  transition: all 0.4s;
}
.portfolio-box .content-area a:hover {
  transition: all 0.4s;
  color: var(--gc-bg-main4);
}
.portfolio-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.08);
  background-color: var(--gc-text-white);
}
.portfolio-box:hover .image-area .image::after {
  opacity: 0.4;
  transition: all 0.4s;
}
.portfolio-box:hover .arrow {
  opacity: 1;
  transform: scale(1) rotate(-45deg);
  transition: all 0.4s;
}
.portfolio-box:hover .content-area span {
  transition: all 0.4s;
  color: var(--gc-bg-main);
}

/*
 ::::::::::::::::::::::::::
  others AREA CSS
 ::::::::::::::::::::::::::
 */
.our-techonoly {
  padding: 80px 0 40px;
  text-align: center;
}
.our-techonoly .stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  margin-top: 40px;
}
.our-techonoly .stat-card {
  text-align: center;
  padding: 20px;
  width: 220px;
  margin: 10px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.our-techonoly .stat-card .stat-number {
  font-size: 45px;
  font-weight: 900;
  color: #ed2224;
  margin-bottom: 0;
  line-height: 40px;
}
.our-techonoly .stat-card .stat-description {
  color: #222;
  font-size: 14px;
  line-height: 22px;
  font-weight: 700;
  text-transform: uppercase;
  text-align: left;
}

.features-section {
  padding-bottom: 50px;
}
.features-section .features-container {
  display: flex;
}
.features-section .features-container .feature-tabs {
  width: 488px;
  padding-right: 20px;
  margin-top: 128px;
}
.features-section .features-container .feature-tabs ul li {
  font-weight: 400;
  font-family: var(--font-inter);
  font-size: 18px;
  line-height: 1.5em;
  padding: 11px 12px;
}
.features-section .features-container .feature-tabs ul li .nav-link {
  border: none;
  font-weight: 600;
  text-decoration: none;
  display: flex;
  padding: 24px;
  flex-direction: row;
  align-items: center;
  gap: 24px;
  border-radius: 12px;
  background: #FFF;
  color: #222;
}
.features-section .features-container .feature-tabs ul li .nav-link.active {
  background-color: white;
  box-shadow: 0 2px 24px 0 rgba(110, 123, 129, 0.2);
}
.features-section .features-container .feature-tabs ul li .nav-link img {
  width: 48px;
}
.features-section .features-container .feature-tabs ul li .nav-link h4 {
  font-size: 24px;
  margin-bottom: 0;
  letter-spacing: -1.152px;
  color: var(--gc-text-title1);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-52);
}
.features-section .features-container .tab-content-area {
  flex: 1;
}
.features-section .features-container .tab-content-area .tab-content-header {
  min-height: 257px;
  text-align: center;
  margin-bottom: -128px;
}
.features-section .features-container .tab-content-area .tab-content-header img {
  max-width: 424px;
  border-radius: 12px;
  background: #F1F3F6;
  box-shadow: 0 0 12px 0 rgba(55, 64, 70, 0.15);
}
.features-section .features-container .tab-content-area .tab-main-content {
  display: flex;
  gap: 32px;
  flex-direction: column;
  background-color: #202020;
  color: #ffffff;
  border-radius: 12px;
  padding: 164px 40px 40px 40px;
}
.features-section .features-container .tab-content-area .tab-main-content .tab-content-title {
  font-size: 24px;
  font-weight: 600;
  text-align: center;
}
.features-section .features-container .tab-content-area .tab-main-content .tab-content-text {
  font-size: 18px;
  line-height: 1.6;
}

@media (max-width: 992px) {
  .features-container {
    flex-direction: column;
  }
  .feature-tabs {
    max-width: 100%;
  }
}
.our-expertise {
  padding: 80px 0;
}
.our-expertise .expertise-items li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  color: #202020;
}
.our-expertise .expertise-items li .check {
  display: inline-block;
  height: 25px;
  width: 25px;
  text-align: center;
  line-height: 25px;
  border-radius: 50%;
  background-color: var(--gc-bg-main);
  color: var(--gc-bg-white);
  font-size: 15px;
  margin-right: 10px;
  margin-top: 4px;
  flex-shrink: 0;
}
.our-expertise .colored-flex-boxes-cards-list {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  gap: 24px;
}
.our-expertise .colored-flex-boxes-cards-list .card.flex-box-card-50 {
  flex: 2 1 calc(50% - 24px);
  max-width: 100%;
}
.our-expertise .colored-flex-boxes-cards-list .card {
  min-width: 250px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  background: #f3f3f3;
  padding: 0;
  border: none;
}
.our-expertise .colored-flex-boxes-cards-list .card:not(.technology-flex-box-card) .content {
  display: flex;
  flex-direction: column;
  max-width: 100%;
  padding: 16px 24px 16px 24px;
  flex: 1 1 calc(80% - 100px);
  min-width: 150px;
}
.our-expertise .colored-flex-boxes-cards-list .card:not(.technology-flex-box-card) .content h3 {
  font-size: 32px;
  font-style: normal;
  font-weight: 800;
  line-height: 40px;
  letter-spacing: -1.152px;
}
.our-expertise .colored-flex-boxes-cards-list .card:not(.technology-flex-box-card) .content p {
  margin-bottom: 0;
  font-weight: 600;
  color: #6E7B81;
  word-break: break-word;
}

/* Responsive adjustments */
/*
 ::::::::::::::::::::::::::
  CONTACT AREA CSS
 ::::::::::::::::::::::::::
 */
.contact2-form .single-input {
  margin-top: 20px;
}
.contact2-form .single-input input,
.contact2-form .single-input textarea {
  border: none;
  background-color: var(--gc-bg-common-5);
  border-radius: 4px;
  padding: 16px;
  width: 100%;
}
.contact2-form .single-input input::-moz-placeholder, .contact2-form .single-input textarea::-moz-placeholder {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16);
  /* 100% */
}
.contact2-form .single-input input::placeholder,
.contact2-form .single-input textarea::placeholder {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16);
  /* 100% */
}
.contact2-form .single-input input:focus,
.contact2-form .single-input textarea:focus {
  outline-color: var(--gc-bg-main4);
  outline-width: 1px;
  outline-style: solid;
}
.contact2-form .single-input input:focus::-moz-placeholder, .contact2-form .single-input textarea:focus::-moz-placeholder {
  opacity: 0;
}
.contact2-form .single-input input:focus::placeholder,
.contact2-form .single-input textarea:focus::placeholder {
  opacity: 0;
}

.contact4-images {
  position: relative;
}
.contact4-images .shape {
  position: absolute;
  top: -30px;
  right: -30px;
}

.contact4-form-area .single-input {
  margin-top: 20px;
}
.contact4-form-area .single-input input,
.contact4-form-area .single-input textarea {
  border: none;
  background-color: var(--gc-bg-common-5);
  border-radius: 4px;
  padding: 16px;
  width: 100%;
  position: relative;
  z-index: 1;
}
.contact4-form-area .single-input input::-moz-placeholder, .contact4-form-area .single-input textarea::-moz-placeholder {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16);
  /* 100% */
  opacity: 0.8;
}
.contact4-form-area .single-input input::placeholder,
.contact4-form-area .single-input textarea::placeholder {
  color: var(--gc-text-pera1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16);
  /* 100% */
  opacity: 0.8;
}
.contact4-form-area .single-input input:focus,
.contact4-form-area .single-input textarea:focus {
  outline-color: var(--gc-bg-main6);
  outline-width: 1px;
  outline-style: solid;
}
.contact4-form-area .single-input input:focus::-moz-placeholder, .contact4-form-area .single-input textarea:focus::-moz-placeholder {
  opacity: 0;
}
.contact4-form-area .single-input input:focus::placeholder,
.contact4-form-area .single-input textarea:focus::placeholder {
  opacity: 0;
}

/*
 ::::::::::::::::::::::::::
  CONTACT AREA CSS
 ::::::::::::::::::::::::::
 */
.contact-location .row.upset.shape-numm .shape-loc .office-card {
  margin-bottom: 15px;
  border: 1px solid #fff2f2;
  background: #fdfdfd;
  border-radius: 12px;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .skyline-img {
  text-align: center;
  background: #fff2f2;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 3.4px 2.7px -30px rgba(0, 0, 0, 0.059), 0 8.2px 8.9px -30px rgba(0, 0, 0, 0.071), 0 25px 40px -30px rgba(0, 0, 0, 0.2);
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text {
  padding: 20px;
  position: relative;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text h4 {
  margin: 0 0 10px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 20px;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text p {
  margin: 0;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text p a {
  font-size: 15px;
  margin-left: 10px;
  color: #ec2024;
  font-weight: 600;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text .pin-container {
  position: absolute;
  z-index: 99;
  right: 40px;
  bottom: 51px;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text .pin-container .pin {
  width: 30px;
  height: 30px;
  border-radius: 50% 50% 50% 0;
  background: #ec2024;
  position: absolute;
  transform: rotate(-45deg);
  left: 50%;
  top: 50%;
  margin: -20px 0 0 -20px;
  animation: bounce 1s both;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text .pin-container .pin:after {
  content: "";
  width: 14px;
  height: 14px;
  margin: 8px 0 0 8px;
  background: #ffffff;
  position: absolute;
  border-radius: 50%;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text .pin-container .pulse {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  height: 14px;
  width: 14px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin: 11px 0 0 -12px;
  transform: rotateX(55deg);
  z-index: -2;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text .pin-container .pulse:after {
  content: "";
  border-radius: 50%;
  height: 40px;
  width: 40px;
  position: absolute;
  margin: -13px 0 0 -13px;
  animation: pulsate 1s ease-out infinite;
  opacity: 0;
  box-shadow: 0 0 1px 2px #89849b;
  animation-delay: 1.1s;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text .pin-container .map-box {
  height: 0;
  opacity: 0;
  width: 180px;
  position: absolute;
  right: -50px;
  top: 20px;
  overflow: hidden;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.5s;
  z-index: -10;
  border-radius: 12px;
}
.contact-location .row.upset.shape-numm .shape-loc .office-card .office-text .pin-container:hover .map-box {
  height: 120px;
  opacity: 1;
}

.contact-content {
  padding: 40px;
  border-radius: 12px;
  background: #fff2f2;
  min-height: 457px;
  box-shadow: 0 3.4px 2.7px -30px rgba(0, 0, 0, 0.059), 0 8.2px 8.9px -30px rgba(0, 0, 0, 0.071), 0 25px 40px -30px rgba(0, 0, 0, 0.2);
}

@keyframes pulsate {
  0% {
    transform: scale(0.1);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}
@keyframes bounce {
  0% {
    opacity: 0;
    transform: translateY(-2000px) rotate(-45deg);
  }
  60% {
    opacity: 1;
    transform: translateY(30px) rotate(-45deg);
  }
  80% {
    transform: translateY(-10px) rotate(-45deg);
  }
  100% {
    transform: translateY(0) rotate(-45deg);
  }
}
/*
 ::::::::::::::::::::::::::
  PRICING AREA CSS
 ::::::::::::::::::::::::::
 */

.pricing-top-section {
  padding: 20px;
  border-bottom: 3px solid #ec2024;
  background-color: #ffffff;
  box-shadow: 0 0 2rem rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.price-top-box {
  border-right: 1px solid #e2e2e2;
  padding: 10px 20px;
}
.price-top-box h3 {
  font-size: 20px;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 25px;
  margin: 0 0 5px 0;
}
.price-top-box p {
  min-height: 50px;
}
.price-top-box h2 {
  font-size: 35px;
  font-weight: 900;
  margin: 0 0 10px 0;
}
.price-top-box h2 span {
  font-size: 23px;
  font-weight: 600;
  text-transform: uppercase;
}
.price-top-box h2 span:last-child {
  font-size: 15px;
  text-transform: inherit;
}
.price-top-box .btn-get-started {
  width: 100%;
  font-size: 18px;
  text-transform: uppercase;
  font-weight: 600;
  margin-top: 0;
  border-radius: 12px;
}
.price-top-box ul {
  position: relative;
  margin-top: 15px;
}
.price-top-box ul li {
  position: relative;
  margin-bottom: 5px;
  padding-left: 30px;
}
.price-top-box ul li::before {
  content: "\f00c";
  font-family: "Font Awesome 5 Pro";
  position: absolute;
  left: 0;
  top: -1px;
  color: #1f1f1f;
  font-weight: 400;
  font-size: 18px;
}

.no-br {
  border-right: none;
}

.gc-pricing-table {
  border-bottom: none;
  border-top: 0;
  display: table;
  margin-bottom: 0;
  max-width: 100%;
  position: relative;
  text-align: center;
  padding: 3rem 0;
}
.gc-pricing-table table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  table-layout: fixed;
}
.gc-pricing-table td,
.gc-pricing-table th,
.gc-pricing-table tr {
  font-size: 16px;
  border: none;
  color: #1f1f1f;
  padding: 1rem 1.25rem;
}
.gc-pricing-table td:hover,
.gc-pricing-table th:hover,
.gc-pricing-table tr:hover {
  color: #1d1d1d;
}
.gc-pricing-table td:first-child {
  border-left: none;
  text-align: left;
  padding: 1rem 1.25rem;
  background: none !important;
}
.gc-pricing-table td:nth-child(2),
.gc-pricing-table td:nth-child(4),
.gc-pricing-table td:nth-child(6),
.gc-pricing-table td:nth-child(8) {
  position: relative;
}
.gc-pricing-table td:nth-child(2)::before,
.gc-pricing-table td:nth-child(4)::before,
.gc-pricing-table td:nth-child(6)::before,
.gc-pricing-table td:nth-child(8)::before {
  background-color: #f5f5f5;
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
}
.gc-pricing-table td i {
  font-size: 18px;
  font-weight: 400;
}
.gc-pricing-table td i.fa-times {
  color: #ff0000;
}
.gc-pricing-table thead,
.gc-pricing-table tfoot {
  text-align: center;
}
.gc-pricing-table thead th {
  text-align: center;
  text-transform: uppercase;
  font-size: 20px;
  letter-spacing: 1px;
}
.gc-pricing-table thead th strong {
  font-weight: 600;
}
.gc-pricing-table__header {
  position: sticky;
  top: 68px;
  z-index: 100;
}
.gc-pricing-table__header:nth-child(2), .gc-pricing-table__header:nth-child(4), .gc-pricing-table__header:nth-child(6), .gc-pricing-table__header:nth-child(8) {
  background-color: #dbdbdb;
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.2);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}
.gc-pricing-table__spacer {
  width: 1rem;
  background: none !important;
  padding: 0 !important;
}
.gc-pricing-table__spacer.v--last-column {
  width: 1rem;
}
.gc-pricing-table__row--feature {
  position: relative;
}
.gc-pricing-table__row--feature td,
.gc-pricing-table__row--feature td:first-child {
  padding: 0.7rem 1.25rem;
  border-bottom: 1px solid #eaeaea;
}
.gc-pricing-table__row--feature:hover {
  background-color: rgba(105, 105, 105, 0.15);
}
.gc-pricing-table__row--header td {
  padding-top: 2rem !important;
  padding-bottom: 0.5rem !important;
}
.gc-pricing-table__row--header td strong {
  font-size: 20px;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 25px;
  margin: 0 0 5px;
}
.gc-pricing-table__footer th {
  font-weight: 400;
  padding-top: 2rem;
}
.gc-pricing-table__footer th:nth-child(2), .gc-pricing-table__footer th:nth-child(4), .gc-pricing-table__footer th:nth-child(6), .gc-pricing-table__footer th:nth-child(8) {
  background: #f5f5f5;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

@media (max-width: 767.98px) {
  .gc-pricing-table thead th {
    text-align: center;
    text-transform: capitalize;
    font-size: 13px;
    padding: 10px 0;
  }
  .gc-pricing-table__row--feature td,
  .gc-pricing-table__row--feature td:first-child {
    padding: 5px;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .gc-pricing-table__signup a {
    border: 1px solid #b1b1b1;
    text-align: center;
    padding: 0;
  }
}
.u-padding-top--x-large {
  padding-top: 2rem !important;
}

/*
 ::::::::::::::::::::::::::
  SHOP AREA CSS
 ::::::::::::::::::::::::::
*/

/*
 ::::::::::::::::::::::::::
  SHOP AREA CSS
 ::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  FEATURES AREA CSS
 ::::::::::::::::::::::::::
 */

.features {
  position: relative;
  z-index: 2;
}

/*
::::::::::::::::::::::::::
 FEATURES AREA CSS
::::::::::::::::::::::::::
*/

/*# sourceMappingURL=main.css.map*/