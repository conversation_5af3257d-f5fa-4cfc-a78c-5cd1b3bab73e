<style>
    .case-study-image {
    border: 1px solid #f4f4f4;
    box-shadow: 0 0 10px 11px #00000008;
}
</style>
<div class="inner-hero" style="background-image: url({{ Theme::asset()->url('img/bg/inner-hero-bg.jpg') }});">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 m-auto text-center">
                <div class="inner-main-heading">
                    <h1 class="text-white">{{ $caseStudy->name }}</h1>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Case Study Content -->
<div class="case-study-content">
    <div class="overview-section sp">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    @if($caseStudy->client_logo)
                    <div class="case-study-logo-image _relative overflow-hidden reveal sec-bg1 border-radius">
                        <img src="{{ RvMedia::getImageUrl($caseStudy->client_logo) }}" alt="{{ $caseStudy->client_name }}" class="img-fluid">
                    </div>
                    @endif
                    <div class="heading1">
                        <h2 class="text-anime-style-3">Overview</h2>
                    </div>
                    <ul class="case-study-list">
                        <li><strong>Client:</strong> {{ $caseStudy->client_name }}</li>
                        <li><strong>Service Offered:</strong> {{ $caseStudy->service_offered }}</li>
                        <li><strong>Key Results:</strong> {{ $caseStudy->key_results }}</li>
                        <li><strong>Duration:</strong> {{ $caseStudy->period }}</li>
                        <li><strong>Keywords:</strong> {{ $caseStudy->keywords }}</li>
                    </ul>
                </div>
                <div class="col-md-8">
                    <div class="case-study-image _relative overflow-hidden reveal sec-bg1 border-radius">
                        <img src="{{ RvMedia::getImageUrl($caseStudy->image) }}" alt="{{ $caseStudy->name }}" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="sp sec-bg1 case-study-about-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    @if($caseStudy->content)
                    <div class="case-study-section">
                        <div class="heading1">
                            <h2 class="text-anime-style-3">About {{ $caseStudy->client_name }}</h2>
                        </div>
                        <div class="section-content">
                            <div class="ck-content">
                                {!! BaseHelper::clean($caseStudy->content) !!}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div class="sec-bg1 case-study-challenge-section">
        <div class="container">
            <div class="row">

                <div class="col-lg-4">
                    @if($caseStudy->challenge)
                    <div class="case-study-section">
                        <div class="heading1">
                            <h2 class="text-anime-style-3">Challenges</h2>
                        </div>
                        <div class="section-content">
                            <div class="ck-content">
                                {!! BaseHelper::clean($caseStudy->challenge) !!}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
                <div class="col-lg-8">
                    <div class="about8-image _relative overflow-hidden reveal">
                        @if($caseStudy->challenge_image)
                            <img src="{{ RvMedia::getImageUrl($caseStudy->challenge_image) }}" alt="{{ $caseStudy->name }} - Challenge">
                        @else
                            <img src="{{ Theme::asset()->url('img/others/a-full-suite-of-SEO-tools.png') }}" alt="">
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="sp sec-bg1 case-study-solutions-section">
        <div class="container">
            <div class="row">

                <div class="col-lg-12">
                    @if($caseStudy->solution)
                    <div class="case-study-section">
                        <div class="heading1">
                            <h2 class="text-anime-style-3">Solutions</h2>
                        </div>
                        <div class="section-content">
                            <div class="ck-content">
                                {!! BaseHelper::clean($caseStudy->solution) !!}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="sp sec-bg1 case-study-results-section pt-0">
        <div class="container">
            <div class="row">

                <div class="col-lg-12">
                    @if($caseStudy->results)
                    <div class="case-study-section">
                        <div class="heading1">
                            <h2 class="text-anime-style-3">Results</h2>
                        </div>
                        <div class="section-content">
                            <div class="ck-content">
                                {!! BaseHelper::clean($caseStudy->results) !!}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="sp sec-bg1 case-study-gallery-section pt-0">
        <div class="container">
            <div class="row">

                <div class="col-lg-12">
                    @php
                        $gallery = gallery_meta_data($caseStudy);
                    @endphp
                    @if($gallery)
                    <div class="case-study-section">
                        <div class="section-content">
                            <div class="row">
                                @foreach($gallery as $image)
                                @if($image)
                                <div class="col-md-4 col-sm-6 mb-3">
                                    <img src="{{ RvMedia::getImageUrl(Arr::get($image, 'img'), 'blog-thumbnail') }}" alt="{{ $caseStudy->name }}" class="img-fluid">
                                </div>
                                @endif
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>


</div>

